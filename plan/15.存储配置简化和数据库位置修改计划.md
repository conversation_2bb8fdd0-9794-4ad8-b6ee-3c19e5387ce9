# 存储配置简化和数据库位置修改计划

## 目标概述

根据用户需求，实现以下两个核心目标：
1. **删除分类文件夹选择弹窗**：移除首次启动时询问是否使用分类独立文件夹的对话框，固定使用分类独立文件夹结构
2. **数据库位置迁移**：将数据库从用户数据目录迁移到用户选择的存储文件夹根目录，支持现有数据库检测和迁移

## 详细任务清单

**开发方法**: 严格遵循TDD（测试驱动开发）流程
- 🔴 **红阶段**: 先编写失败的测试用例
- 🟢 **绿阶段**: 编写最小化代码使测试通过
- 🔄 **重构阶段**: 优化代码结构，确保测试仍然通过

### 阶段一：移除分类文件夹选择功能

#### [x] 1. 修改首次启动对话框逻辑（直接实现）
**文件**: `/electron/main.ts`
**目标**: 简化 `showFirstTimeSetupDialog()` 函数，移除分类文件夹结构选择
- 删除第64-76行的分类文件夹结构询问对话框
- 移除所有与 `usesCategoryFolders` 相关的代码
- 更新确认信息显示，移除文件夹结构相关描述
- 保持存储位置选择功能不变
- **说明**: 这是一次性代码清理，不需要专门的测试，通过手动验证即可

#### [x] 2. 彻底移除usesCategoryFolders配置项（直接实现）
**文件**: `/electron/services/SettingsService.ts`
**目标**: 完全移除 `usesCategoryFolders` 配置项
- 从 `StorageSettings` 接口中删除 `usesCategoryFolders` 字段
- 从默认配置中移除相关配置项
- 更新所有使用该配置的代码，直接假设使用分类文件夹结构
- 清理配置验证和迁移逻辑中的相关代码
- **说明**: 这是一次性配置清理，不需要专门测试

#### [x] 3. 全面清理usesCategoryFolders残留代码（直接实现）
**目标**: 清理所有与 `usesCategoryFolders` 相关的代码和逻辑
- 搜索整个代码库中所有引用 `usesCategoryFolders` 的地方
- 移除相关的条件判断逻辑，直接使用分类文件夹结构
- 检查ImageService中是否有相关的分支逻辑需要简化
- 更新任何相关的类型定义和接口
- **说明**: 这是一次性代码清理，依赖现有的服务测试覆盖

### 阶段二：数据库位置迁移（核心功能，需要TDD）

#### [x] 4. 编写数据库迁移服务测试
**文件**: `/__tests__/electron/database/DatabaseMigration.test.ts`
**TDD阶段**: 🔴 红阶段
**目标**: 编写测试验证数据库迁移核心功能
- ✅ 测试数据库文件检测和复制功能
- ✅ 测试迁移过程的原子性（成功或完全回滚）
- ✅ 测试多次迁移的幂等性
- ✅ 测试迁移失败时的错误处理

#### [x] 5. 实现数据库迁移服务
**文件**: `/electron/database/index.ts`
**TDD阶段**: 🟢 绿阶段
**目标**: 实现数据库迁移核心逻辑，使测试通过
- ✅ 修改DatabaseManager构造函数支持自定义数据库路径
- ✅ 实现数据库文件检测和迁移逻辑
- ✅ 添加完整的错误处理和回滚机制
- ✅ 支持迁移进度反馈
- ✅ 添加createFromSettings静态工厂方法

#### [x] 6. 更新主进程数据库初始化（直接实现）
**文件**: `/electron/main.ts`
**目标**: 修改主进程中的数据库初始化逻辑
- 确保在SettingsService初始化后才初始化DatabaseManager
- 将存储路径传递给DatabaseManager构造函数
- 处理数据库迁移可能出现的错误和异常情况
- **说明**: 依赖数据库迁移服务的测试覆盖

#### [x] 7. 更新存储位置更改时的数据库迁移（直接实现）
**文件**: `/electron/main.ts` 中的 `changeStorageLocation()` 函数
**目标**: 确保更改存储位置时同时迁移数据库
- 在存储位置迁移逻辑中添加数据库迁移步骤
- 确保数据库迁移完成后才更新设置
- 添加数据库迁移失败的回滚机制
- **说明**: 依赖数据库迁移服务的测试覆盖

#### [x] 8. 重构和优化代码
**TDD阶段**: 🔄 重构阶段
**目标**: 优化实现后的代码结构
- 重构数据库迁移逻辑，提高代码复用性
- 优化错误处理和日志记录
- 确保所有测试仍然通过

### 阶段三：验证和回归测试

#### [x] 9. 手动验证首次启动流程
**目标**: 验证修改后的首次启动流程正常工作
- 测试新用户首次启动时的存储配置流程
- 验证不再询问分类文件夹结构选择
- 验证数据库在正确位置创建
- 测试不同存储位置选择的场景
- **说明**: 手动验证即可，无需专门测试

#### [x] 10. 完整功能回归测试
**目标**: 确保所有现有功能正常工作
- 运行现有的所有测试套件：`npm test`
- 运行代码质量检查：`npm run check-all`
- 手动测试图片上传和分类管理功能
- 手动测试存储位置更改功能
- 手动测试数据库备份和恢复功能
- 验证OSS存储模式是否受影响
- **说明**: 依赖现有测试覆盖 + 关键功能手动验证

## 技术实现细节

### 数据库路径计算逻辑
```typescript
// 新的数据库路径计算
const storagePath = settingsService.getStoragePath();
const dbPath = path.join(storagePath, 'database.db');

// 迁移检查逻辑
const oldDbPath = path.join(app.getPath('userData'), 'database.db');
if (!fs.existsSync(dbPath) && fs.existsSync(oldDbPath)) {
  // 执行迁移
  fs.copyFileSync(oldDbPath, dbPath);
  console.log('数据库已迁移到新位置');
}
```

### 首次启动简化流程
```typescript
// 简化后的首次启动逻辑
async function showFirstTimeSetupDialog(): Promise<boolean> {
  // 1. 显示欢迎信息
  // 2. 选择存储位置（自定义或默认）
  // 3. 保存设置（不再需要usesCategoryFolders配置）
  // 4. 显示确认信息
}
```

## 风险评估

### 低风险项
- 移除分类文件夹选择对话框：不会影响现有数据
- 数据库路径修改：通过复制文件保证数据安全

### 中风险项
- 数据库迁移失败：需要完善的错误处理和回滚机制
- 存储位置更改时的数据一致性：需要确保数据库和图片文件同步迁移

## 验收标准

1. ✅ **首次启动简化**：新用户首次启动时不再询问分类文件夹结构，代码中移除usesCategoryFolders配置项
2. ✅ **数据库位置迁移**：数据库文件存储在用户选择的存储文件夹根目录
3. ✅ **现有数据兼容**：现有用户的数据库能够自动迁移到新位置
4. ✅ **功能完整性**：所有现有功能正常工作，无破坏性变更
5. ✅ **错误处理**：数据库迁移失败时有适当的错误提示和回滚机制

## 实施约束

- **保持API兼容**：不修改前端API接口和数据结构
- **向后兼容**：确保现有用户数据无损迁移
- **原子操作**：数据库迁移要么完全成功，要么完全回滚
- **用户体验**：迁移过程对用户透明，不影响正常使用

---

**开发负责人**: Claude Code Assistant  
**计划创建时间**: 2025-07-17  
**预计完成时间**: 2025-07-17  
**完成进度**: 10/10 任务已完成 🎉

## TDD与直接实现的平衡策略

### 🧪 需要TDD的情况
**核心业务逻辑，需要持续维护和扩展**：
- 数据库迁移服务（任务4-5）：复杂的文件操作和错误处理逻辑
- 关键算法和数据处理逻辑
- 可能频繁变更的业务规则

### 🔧 直接实现的情况
**一次性代码修改，清理和重构**：
- 移除usesCategoryFolders配置项（任务1-3）：一次性清理工作
- 主进程初始化更新（任务6-7）：简单的配置修改
- UI对话框修改：一次性用户界面调整

### 🔍 验证策略
- **现有测试覆盖**：依赖项目现有的测试套件
- **手动验证**：对于UI和一次性修改，通过手动测试验证
- **回归测试**：确保现有功能不受影响

### TDD红→绿→重构循环（仅用于核心功能）
1. **🔴 红阶段**：编写失败的测试用例，覆盖边界情况
2. **🟢 绿阶段**：编写最小化代码使测试通过
3. **🔄 重构阶段**：优化代码结构和可读性

这种平衡策略避免了"为了测试而测试"，专注于真正需要测试覆盖的核心功能。