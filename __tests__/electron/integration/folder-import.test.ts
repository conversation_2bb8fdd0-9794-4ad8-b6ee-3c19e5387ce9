import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { DatabaseManager } from '../../../electron/database';
import { CategoryService } from '../../../electron/services/CategoryService';
import { ImageService } from '../../../electron/services/ImageService';
import { FolderStructureValidator } from '../../../electron/services/FolderStructureValidator';
import { BatchImportService } from '../../../electron/services/BatchImportService';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

// Mock file system operations
vi.mock('fs');
vi.mock('path');
vi.mock('util');
vi.mock('../../../electron/services/FolderStructureValidator');
vi.mock('../../../electron/services/CategoryService');
vi.mock('../../../electron/services/ImageService');

describe('Folder Import Integration Tests', () => {
  let dbManager: DatabaseManager;
  let categoryService: CategoryService;
  let imageService: ImageService;
  let folderValidator: FolderStructureValidator;
  let batchImportService: BatchImportService;

  beforeEach(() => {
    // Clear all mocks
    vi.clearAllMocks();

    // Create mock database manager
    dbManager = {
      getDatabase: vi.fn(),
      resetDatabase: vi.fn(),
    } as any;

    // Create mock services
    categoryService = {
      createCategory: vi.fn(),
    } as any;

    imageService = {
      uploadImage: vi.fn(),
    } as any;

    folderValidator = {
      validateFolderStructure: vi.fn(),
      isSupportedImageFormat: vi.fn(),
    } as any;

    // Mock constructors
    vi.mocked(CategoryService).mockImplementation(() => categoryService);
    vi.mocked(ImageService).mockImplementation(() => imageService);
    vi.mocked(FolderStructureValidator).mockImplementation(() => folderValidator);

    // Mock promisify for async fs operations
    vi.mocked(promisify).mockImplementation((fn: any) => {
      if (fn === fs.readFile) {
        return vi.fn().mockResolvedValue(Buffer.from('fake-image-data'));
      }
      if (fn === fs.readdir) {
        return vi.fn().mockImplementation((dirPath: string) => {
          if (dirPath.includes('birds')) {
            return Promise.resolve(['bird1.jpg', 'bird2.png', 'bird3.gif']);
          }
          if (dirPath.includes('flowers')) {
            return Promise.resolve(['flower1.jpg', 'flower2.png']);
          }
          return Promise.resolve(['image1.jpg', 'image2.png', 'image3.gif']);
        });
      }
      if (fn === fs.stat) {
        return vi.fn().mockResolvedValue({ size: 1024 * 1024 }); // 1MB
      }
      return fn;
    });

    // Create real BatchImportService with mocked dependencies
    batchImportService = new BatchImportService(dbManager);

    // Mock file system operations
    vi.mocked(fs.existsSync).mockReturnValue(true);
    vi.mocked(fs.statSync).mockReturnValue({ isDirectory: () => false, isFile: () => true } as any);
    vi.mocked(fs.readdirSync).mockImplementation((dirPath: any) => {
      const pathStr = String(dirPath);
      if (pathStr.includes('birds')) {
        return ['bird1.jpg', 'bird2.png', 'bird3.gif'] as any;
      }
      if (pathStr.includes('flowers')) {
        return ['flower1.jpg', 'flower2.png'] as any;
      }
      return ['image1.jpg', 'image2.png', 'image3.gif'] as any;
    });
    vi.mocked(fs.readFileSync).mockReturnValue(Buffer.from('fake-image-data'));
    vi.mocked(path.join).mockImplementation((...args) => args.join('/'));
    vi.mocked(path.extname).mockImplementation((filepath) => {
      if (filepath.includes('.jpg')) return '.jpg';
      if (filepath.includes('.png')) return '.png';
      if (filepath.includes('.gif')) return '.gif';
      return '';
    });
    
    // Mock folder validator methods
    vi.mocked(folderValidator.isSupportedImageFormat).mockReturnValue(true);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Basic Import Workflow', () => {
    it('should successfully complete entire import workflow', async () => {
      const testFolderPath = '/test/folder';
      const importOptions = {
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock folder validation
      vi.mocked(folderValidator.validateFolderStructure).mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 3, path: '/test/folder/birds' },
          { name: 'flowers', imageCount: 2, path: '/test/folder/flowers' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock database reset
      vi.mocked(dbManager.resetDatabase).mockResolvedValue({ 
        success: true,
        message: 'Database reset successful'
      });

      // Mock category creation
      vi.mocked(categoryService.createCategory).mockImplementation(async (categoryData) => {
        return {
          id: `category-${categoryData.name}`,
          name: categoryData.name,
          description: categoryData.description,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          thumbnail_path: null,
          thumbnail_url: null,
        };
      });

      // Mock image upload
      vi.mocked(imageService.uploadImage).mockImplementation(async (categoryId, fileBuffer, originalFilename) => {
        return {
          id: `image-${originalFilename}`,
          original_filename: originalFilename,
          stored_filename: `stored-${originalFilename}`,
          file_size: fileBuffer.length,
          mime_type: 'image/jpeg',
          width: 800,
          height: 600,
          category_id: categoryId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      });

      // Track progress events
      const progressEvents: any[] = [];
      const progressCallback = (progress: any) => {
        progressEvents.push(progress);
      };

      // Execute the complete workflow
      const result = await batchImportService.importFromFolder(
        testFolderPath,
        importOptions,
        progressCallback
      );

      // Verify the result
      expect(result.success).toBe(true);
      expect(result.totalCategories).toBe(2);
      expect(result.importedCategories).toBe(2);
      expect(result.totalImages).toBe(5);
      expect(result.errors).toHaveLength(0);

      // Verify database was reset
      expect(dbManager.resetDatabase).toHaveBeenCalled();

      // Verify categories were created
      expect(categoryService.createCategory).toHaveBeenCalledTimes(2);
      expect(categoryService.createCategory).toHaveBeenCalledWith({ name: 'birds', description: null });
      expect(categoryService.createCategory).toHaveBeenCalledWith({ name: 'flowers', description: null });

      // Verify progress events
      expect(progressEvents.length).toBeGreaterThan(0);
      expect(progressEvents[0].stage).toBe('validation');
      expect(progressEvents[progressEvents.length - 1].stage).toBe('complete');
      expect(progressEvents[progressEvents.length - 1].progress).toBe(100);
    });

    it('should handle folder structure validation failure', async () => {
      const testFolderPath = '/nonexistent/folder';
      const importOptions = {
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock folder validation failure
      vi.mocked(folderValidator.validateFolderStructure).mockResolvedValue({
        isValid: false,
        categories: [],
        errors: ['文件夹不存在'],
        warnings: [],
      });

      const progressCallback = vi.fn();

      const result = await batchImportService.importFromFolder(
        testFolderPath,
        importOptions,
        progressCallback
      );

      expect(result.success).toBe(false);
      expect(result.errors).toContain('文件夹结构验证失败: 文件夹不存在');
      expect(dbManager.resetDatabase).not.toHaveBeenCalled();
    });

    it('should handle database reset failure', async () => {
      const testFolderPath = '/test/folder';
      const importOptions = {
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock folder validation success first
      vi.mocked(folderValidator.validateFolderStructure).mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 3, path: '/test/folder/birds' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock database reset failure
      vi.mocked(dbManager.resetDatabase).mockResolvedValue({ 
        success: false,
        message: 'Database reset failed'
      });

      const progressCallback = vi.fn();

      const result = await batchImportService.importFromFolder(
        testFolderPath,
        importOptions,
        progressCallback
      );

      expect(result.success).toBe(false);
      expect(result.errors).toContain('数据库重置失败: Database reset failed');
    });

    it('should handle category creation failure', async () => {
      const testFolderPath = '/test/folder';
      const importOptions = {
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock folder validation success first
      vi.mocked(folderValidator.validateFolderStructure).mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 3, path: '/test/folder/birds' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock database reset success
      vi.mocked(dbManager.resetDatabase).mockResolvedValue({ 
        success: true,
        message: 'Database reset successful'
      });

      // Mock category creation failure
      vi.mocked(categoryService.createCategory).mockRejectedValue(new Error('Category creation failed'));

      const progressCallback = vi.fn();

      const result = await batchImportService.importFromFolder(
        testFolderPath,
        importOptions,
        progressCallback
      );

      expect(result.success).toBe(false);
      expect(result.errors).toContain('创建分类 "birds" 失败: Category creation failed');
    });

    it('should handle image upload failure gracefully', async () => {
      const testFolderPath = '/test/folder';
      const importOptions = {
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock folder validation success first
      vi.mocked(folderValidator.validateFolderStructure).mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 3, path: '/test/folder/birds' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock database reset success
      vi.mocked(dbManager.resetDatabase).mockResolvedValue({ 
        success: true,
        message: 'Database reset successful'
      });

      // Mock category creation success
      vi.mocked(categoryService.createCategory).mockImplementation(async (categoryData) => {
        return {
          id: `category-${categoryData.name}`,
          name: categoryData.name,
          description: categoryData.description,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          thumbnail_path: null,
          thumbnail_url: null,
        };
      });

      // Mock image upload with some failures
      vi.mocked(imageService.uploadImage).mockImplementation(async (categoryId, fileBuffer, originalFilename) => {
        // Simulate failure for specific files
        if (originalFilename.includes('bird2')) {
          throw new Error('Upload failed for ' + originalFilename);
        }
        return {
          id: `image-${originalFilename}`,
          original_filename: originalFilename,
          stored_filename: `stored-${originalFilename}`,
          file_size: fileBuffer.length,
          mime_type: 'image/jpeg',
          width: 800,
          height: 600,
          category_id: categoryId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      });

      const progressCallback = vi.fn();

      const result = await batchImportService.importFromFolder(
        testFolderPath,
        importOptions,
        progressCallback
      );

      expect(result.success).toBe(true); // Should still succeed overall
      expect(result.totalCategories).toBe(1);
      expect(result.importedCategories).toBe(1);
      expect(result.totalImages).toBe(3);
      expect(result.importedImages).toBe(2); // 1 failed upload
      expect(result.failedImages).toBe(1);
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0]).toContain('图片上传失败: bird2');
    });

    it('should report progress correctly', async () => {
      const testFolderPath = '/test/folder';
      const importOptions = {
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock folder validation success first
      vi.mocked(folderValidator.validateFolderStructure).mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 2, path: '/test/folder/birds' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock database reset success
      vi.mocked(dbManager.resetDatabase).mockResolvedValue({ 
        success: true,
        message: 'Database reset successful'
      });

      // Mock category creation success
      vi.mocked(categoryService.createCategory).mockImplementation(async (categoryData) => {
        return {
          id: `category-${categoryData.name}`,
          name: categoryData.name,
          description: categoryData.description,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          thumbnail_path: null,
          thumbnail_url: null,
        };
      });

      // Mock image upload success
      vi.mocked(imageService.uploadImage).mockImplementation(async (categoryId, fileBuffer, originalFilename) => {
        return {
          id: `image-${originalFilename}`,
          original_filename: originalFilename,
          stored_filename: `stored-${originalFilename}`,
          file_size: fileBuffer.length,
          mime_type: 'image/jpeg',
          width: 800,
          height: 600,
          category_id: categoryId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      });

      const progressCallback = vi.fn();

      const result = await batchImportService.importFromFolder(
        testFolderPath,
        importOptions,
        progressCallback
      );

      expect(result.success).toBe(true);
      expect(progressCallback).toHaveBeenCalled();
      
      // Verify progress stages
      const progressCalls = progressCallback.mock.calls.map(call => call[0]);
      const stages = progressCalls.map(progress => progress.stage);
      
      expect(stages).toContain('validation');
      expect(stages).toContain('reset');
      expect(stages).toContain('categories');
      expect(stages).toContain('images');
      expect(stages).toContain('complete');
      
      // Verify final progress
      const finalProgress = progressCalls[progressCalls.length - 1];
      expect(finalProgress.stage).toBe('complete');
      expect(finalProgress.progress).toBe(100);
    });
  });
});