import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ipcMain, BrowserWindow, dialog } from 'electron';
import { BatchImportService } from '../../../electron/services/BatchImportService';
import { FolderStructureValidator } from '../../../electron/services/FolderStructureValidator';
import { DatabaseManager } from '../../../electron/database';

// Mock electron modules
vi.mock('electron', () => ({
  ipcMain: {
    handle: vi.fn(),
    removeAllListeners: vi.fn(),
  },
  BrowserWindow: vi.fn(),
  dialog: {
    showOpenDialog: vi.fn(),
  },
}));

// Mock services
vi.mock('../../../electron/services/BatchImportService');
vi.mock('../../../electron/services/FolderStructureValidator');
vi.mock('../../../electron/database');

describe('Folder Import IPC Handlers', () => {
  let mockBatchImportService: any;
  let mockFolderValidator: any;
  let mockMainWindow: any;
  let ipcHandlers: Map<string, Function>;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Create mock instances
    mockBatchImportService = {
      importFromFolder: vi.fn(),
    };

    mockFolderValidator = {
      validateFolderStructure: vi.fn(),
    };

    mockMainWindow = {
      webContents: {
        send: vi.fn(),
      },
    };

    // Track IPC handlers
    ipcHandlers = new Map();
    vi.mocked(ipcMain.handle).mockImplementation((channel: string, handler: Function) => {
      ipcHandlers.set(channel, handler);
    });

    // Mock constructors
    vi.mocked(BatchImportService).mockImplementation(() => mockBatchImportService);
    vi.mocked(FolderStructureValidator).mockImplementation(() => mockFolderValidator);

    // Setup the IPC handlers as they would be in main.ts
    setupFolderImportIpcHandlers();
  });

  function setupFolderImportIpcHandlers() {
    // Simulate the IPC handler setup from main.ts
    ipcMain.handle('validate-folder-structure', async (_, folderPath: string) => {
      try {
        const result = await mockFolderValidator.validateFolderStructure(folderPath);
        return result;
      } catch (error) {
        console.error('文件夹结构验证失败:', error);
        return {
          isValid: false,
          categories: [],
          errors: [`验证失败: ${error instanceof Error ? error.message : String(error)}`],
          warnings: [],
        };
      }
    });

    ipcMain.handle('import-from-folder', async (_, folderPath: string, options: any) => {
      try {
        const progressHandler = (progress: any) => {
          if (mockMainWindow) {
            mockMainWindow.webContents.send('import-progress', progress);
          }
        };

        const result = await mockBatchImportService.importFromFolder(
          folderPath,
          options,
          progressHandler
        );

        return result;
      } catch (error) {
        console.error('文件夹导入失败:', error);
        return {
          success: false,
          totalCategories: 0,
          importedCategories: 0,
          totalImages: 0,
          importedImages: 0,
          failedImages: 0,
          errors: [`导入失败: ${error instanceof Error ? error.message : String(error)}`],
          warnings: [],
          duration: 0,
        };
      }
    });

    ipcMain.handle('select-import-folder', async () => {
      try {
        const result = await dialog.showOpenDialog(mockMainWindow, {
          title: '选择要导入的文件夹',
          properties: ['openDirectory'],
          buttonLabel: '选择文件夹',
        });

        if (result.canceled || !result.filePaths.length) {
          return null;
        }

        return result.filePaths[0];
      } catch (error) {
        console.error('文件夹选择失败:', error);
        return null;
      }
    });
  }

  describe('validate-folder-structure IPC handler', () => {
    it('should validate folder structure successfully', async () => {
      const testFolderPath = '/test/folder';
      const expectedResult = {
        isValid: true,
        categories: [
          { name: 'category1', imageCount: 5, path: '/test/folder/category1' },
          { name: 'category2', imageCount: 3, path: '/test/folder/category2' },
        ],
        errors: [],
        warnings: [],
      };

      mockFolderValidator.validateFolderStructure.mockResolvedValue(expectedResult);

      const handler = ipcHandlers.get('validate-folder-structure');
      const result = await handler(null, testFolderPath);

      expect(mockFolderValidator.validateFolderStructure).toHaveBeenCalledWith(testFolderPath);
      expect(result).toEqual(expectedResult);
    });

    it('should handle validation errors gracefully', async () => {
      const testFolderPath = '/invalid/folder';
      const error = new Error('Folder does not exist');

      mockFolderValidator.validateFolderStructure.mockRejectedValue(error);

      const handler = ipcHandlers.get('validate-folder-structure');
      const result = await handler(null, testFolderPath);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('验证失败: Folder does not exist');
    });

    it('should handle validation failure result', async () => {
      const testFolderPath = '/empty/folder';
      const validationResult = {
        isValid: false,
        categories: [],
        errors: ['文件夹为空'],
        warnings: [],
      };

      mockFolderValidator.validateFolderStructure.mockResolvedValue(validationResult);

      const handler = ipcHandlers.get('validate-folder-structure');
      const result = await handler(null, testFolderPath);

      expect(result).toEqual(validationResult);
    });
  });

  describe('import-from-folder IPC handler', () => {
    it('should import from folder successfully', async () => {
      const testFolderPath = '/test/folder';
      const testOptions = {
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      const expectedResult = {
        success: true,
        totalCategories: 2,
        importedCategories: 2,
        totalImages: 8,
        importedImages: 8,
        failedImages: 0,
        errors: [],
        warnings: [],
        duration: 5000,
      };

      mockBatchImportService.importFromFolder.mockResolvedValue(expectedResult);

      const handler = ipcHandlers.get('import-from-folder');
      const result = await handler(null, testFolderPath, testOptions);

      expect(mockBatchImportService.importFromFolder).toHaveBeenCalledWith(
        testFolderPath,
        testOptions,
        expect.any(Function)
      );
      expect(result).toEqual(expectedResult);
    });

    it('should handle import errors gracefully', async () => {
      const testFolderPath = '/test/folder';
      const testOptions = { overwriteExisting: true };
      const error = new Error('Import failed');

      mockBatchImportService.importFromFolder.mockRejectedValue(error);

      const handler = ipcHandlers.get('import-from-folder');
      const result = await handler(null, testFolderPath, testOptions);

      expect(result.success).toBe(false);
      expect(result.errors).toContain('导入失败: Import failed');
    });

    it('should send progress updates to main window', async () => {
      const testFolderPath = '/test/folder';
      const testOptions = { overwriteExisting: true };
      const mockProgress = {
        stage: 'validation',
        progress: 50,
        totalCategories: 2,
        completedCategories: 1,
        totalImages: 8,
        completedImages: 4,
        errors: [],
      };

      mockBatchImportService.importFromFolder.mockImplementation(
        (folderPath: string, options: any, progressCallback: Function) => {
          // Simulate progress callback
          progressCallback(mockProgress);
          return Promise.resolve({
            success: true,
            totalCategories: 2,
            importedCategories: 2,
            totalImages: 8,
            importedImages: 8,
            failedImages: 0,
            errors: [],
            warnings: [],
            duration: 3000,
          });
        }
      );

      const handler = ipcHandlers.get('import-from-folder');
      await handler(null, testFolderPath, testOptions);

      expect(mockMainWindow.webContents.send).toHaveBeenCalledWith('import-progress', mockProgress);
    });

    it('should handle import failure result', async () => {
      const testFolderPath = '/test/folder';
      const testOptions = { overwriteExisting: true };
      const failedResult = {
        success: false,
        totalCategories: 2,
        importedCategories: 1,
        totalImages: 8,
        importedImages: 3,
        failedImages: 5,
        errors: ['Database error', 'File permission error'],
        warnings: ['Some warnings'],
        duration: 2000,
      };

      mockBatchImportService.importFromFolder.mockResolvedValue(failedResult);

      const handler = ipcHandlers.get('import-from-folder');
      const result = await handler(null, testFolderPath, testOptions);

      expect(result).toEqual(failedResult);
    });
  });

  describe('select-import-folder IPC handler', () => {
    it('should show folder selection dialog and return selected path', async () => {
      const selectedPath = '/selected/folder';
      const mockDialogResult = {
        canceled: false,
        filePaths: [selectedPath],
      };

      vi.mocked(dialog.showOpenDialog).mockResolvedValue(mockDialogResult);

      const handler = ipcHandlers.get('select-import-folder');
      const result = await handler();

      expect(dialog.showOpenDialog).toHaveBeenCalledWith(mockMainWindow, {
        title: '选择要导入的文件夹',
        properties: ['openDirectory'],
        buttonLabel: '选择文件夹',
      });
      expect(result).toBe(selectedPath);
    });

    it('should return null when user cancels dialog', async () => {
      const mockDialogResult = {
        canceled: true,
        filePaths: [],
      };

      vi.mocked(dialog.showOpenDialog).mockResolvedValue(mockDialogResult);

      const handler = ipcHandlers.get('select-import-folder');
      const result = await handler();

      expect(result).toBeNull();
    });

    it('should return null when no folder is selected', async () => {
      const mockDialogResult = {
        canceled: false,
        filePaths: [],
      };

      vi.mocked(dialog.showOpenDialog).mockResolvedValue(mockDialogResult);

      const handler = ipcHandlers.get('select-import-folder');
      const result = await handler();

      expect(result).toBeNull();
    });

    it('should handle dialog errors gracefully', async () => {
      const error = new Error('Dialog failed');
      vi.mocked(dialog.showOpenDialog).mockRejectedValue(error);

      const handler = ipcHandlers.get('select-import-folder');
      const result = await handler();

      expect(result).toBeNull();
    });
  });

  describe('IPC handler registration', () => {
    it('should register all required IPC handlers', () => {
      expect(ipcHandlers.has('validate-folder-structure')).toBe(true);
      expect(ipcHandlers.has('import-from-folder')).toBe(true);
      expect(ipcHandlers.has('select-import-folder')).toBe(true);
    });

    it('should handle concurrent validation requests', async () => {
      const paths = ['/folder1', '/folder2', '/folder3'];
      const mockResults = paths.map((path, index) => ({
        isValid: true,
        categories: [{ name: `cat${index}`, imageCount: index + 1, path }],
        errors: [],
        warnings: [],
      }));

      mockFolderValidator.validateFolderStructure
        .mockResolvedValueOnce(mockResults[0])
        .mockResolvedValueOnce(mockResults[1])
        .mockResolvedValueOnce(mockResults[2]);

      const handler = ipcHandlers.get('validate-folder-structure');
      const promises = paths.map(path => handler(null, path));
      const results = await Promise.all(promises);

      expect(results).toEqual(mockResults);
      expect(mockFolderValidator.validateFolderStructure).toHaveBeenCalledTimes(3);
    });

    it('should handle concurrent import requests properly', async () => {
      const testFolderPath = '/test/folder';
      const testOptions = { overwriteExisting: true };
      const mockResult = {
        success: true,
        totalCategories: 1,
        importedCategories: 1,
        totalImages: 5,
        importedImages: 5,
        failedImages: 0,
        errors: [],
        warnings: [],
        duration: 1000,
      };

      mockBatchImportService.importFromFolder.mockResolvedValue(mockResult);

      const handler = ipcHandlers.get('import-from-folder');
      
      // Test multiple concurrent calls
      const promises = [
        handler(null, testFolderPath, testOptions),
        handler(null, testFolderPath, testOptions),
      ];
      
      const results = await Promise.all(promises);
      
      expect(results).toEqual([mockResult, mockResult]);
      expect(mockBatchImportService.importFromFolder).toHaveBeenCalledTimes(2);
    });
  });

  describe('Parameter validation', () => {
    it('should handle invalid folder path in validation', async () => {
      const invalidPath = '';
      
      mockFolderValidator.validateFolderStructure.mockResolvedValue({
        isValid: false,
        categories: [],
        errors: ['路径不能为空'],
        warnings: [],
      });

      const handler = ipcHandlers.get('validate-folder-structure');
      const result = await handler(null, invalidPath);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('路径不能为空');
    });

    it('should handle missing options in import', async () => {
      const testFolderPath = '/test/folder';
      const undefinedOptions = undefined;

      mockBatchImportService.importFromFolder.mockResolvedValue({
        success: true,
        totalCategories: 0,
        importedCategories: 0,
        totalImages: 0,
        importedImages: 0,
        failedImages: 0,
        errors: [],
        warnings: [],
        duration: 0,
      });

      const handler = ipcHandlers.get('import-from-folder');
      const result = await handler(null, testFolderPath, undefinedOptions);

      expect(mockBatchImportService.importFromFolder).toHaveBeenCalledWith(
        testFolderPath,
        undefinedOptions,
        expect.any(Function)
      );
      expect(result.success).toBe(true);
    });
  });
});