import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SettingsService, OSSConfig } from '../../../electron/services/SettingsService';
import fs from 'fs';
import path from 'path';

describe('OSS Configuration Validation Tests', () => {
  let settingsService: SettingsService;
  let tempDir: string;

  beforeEach(() => {
    // Create a temporary directory for test settings
    tempDir = path.join(__dirname, '../../../tmp/test-settings');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Mock app.getPath to use our temp directory
    vi.doMock('electron', () => ({
      app: {
        getPath: vi.fn().mockReturnValue(tempDir)
      }
    }));

    settingsService = new SettingsService();
  });

  afterEach(() => {
    // Clean up test files
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    // Clear any mocks
    vi.clearAllMocks();
  });

  describe('OSS Configuration Validation', () => {
    it('should validate complete OSS configuration', () => {
      const validConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'my-pokedex-bucket',
        pathPrefix: 'pokedex-images'
      };

      expect(settingsService.validateOSSConfig(validConfig)).toBe(true);
    });

    it('should validate OSS configuration without optional pathPrefix', () => {
      const validConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'my-pokedex-bucket'
      };

      expect(settingsService.validateOSSConfig(validConfig)).toBe(true);
    });

    it('should reject configuration with missing endpoint', () => {
      const invalidConfig: OSSConfig = {
        endpoint: '',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'my-pokedex-bucket'
      };

      expect(settingsService.validateOSSConfig(invalidConfig)).toBe(false);
    });

    it('should reject configuration with missing region', () => {
      const invalidConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: '',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'my-pokedex-bucket'
      };

      expect(settingsService.validateOSSConfig(invalidConfig)).toBe(false);
    });

    it('should reject configuration with missing accessKeyId', () => {
      const invalidConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: '',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'my-pokedex-bucket'
      };

      expect(settingsService.validateOSSConfig(invalidConfig)).toBe(false);
    });

    it('should reject configuration with missing secretAccessKey', () => {
      const invalidConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: '',
        bucket: 'my-pokedex-bucket'
      };

      expect(settingsService.validateOSSConfig(invalidConfig)).toBe(false);
    });

    it('should reject configuration with missing bucket', () => {
      const invalidConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: ''
      };

      expect(settingsService.validateOSSConfig(invalidConfig)).toBe(false);
    });

    it('should reject configuration with all fields missing', () => {
      const invalidConfig: OSSConfig = {
        endpoint: '',
        region: '',
        accessKeyId: '',
        secretAccessKey: '',
        bucket: ''
      };

      expect(settingsService.validateOSSConfig(invalidConfig)).toBe(false);
    });
  });

  describe('OSS Configuration Storage', () => {
    it('should store and retrieve OSS configuration correctly', () => {
      const ossConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'my-pokedex-bucket',
        pathPrefix: 'pokedex-images'
      };

      // Initially should not be configured
      expect(settingsService.isOSSConfigured()).toBe(false);
      expect(settingsService.getOSSConfig()).toBeUndefined();

      // Store configuration
      const saveResult = settingsService.setOSSConfig(ossConfig);
      expect(saveResult).toBe(true);

      // Verify storage
      expect(settingsService.isOSSConfigured()).toBe(true);
      expect(settingsService.getOSSConfig()).toEqual(ossConfig);
    });

    it('should update existing OSS configuration', () => {
      const initialConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'old-bucket'
      };

      const updatedConfig: OSSConfig = {
        endpoint: 'https://oss-cn-beijing.aliyuncs.com',
        region: 'cn-beijing',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'new-bucket',
        pathPrefix: 'new-prefix'
      };

      // Store initial configuration
      settingsService.setOSSConfig(initialConfig);
      expect(settingsService.getOSSConfig()).toEqual(initialConfig);

      // Update configuration
      settingsService.setOSSConfig(updatedConfig);
      expect(settingsService.getOSSConfig()).toEqual(updatedConfig);
    });

    it('should persist OSS configuration across service instances', () => {
      const ossConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'my-pokedex-bucket',
        pathPrefix: 'pokedex-images'
      };

      // Store configuration with first service instance
      settingsService.setOSSConfig(ossConfig);
      expect(settingsService.getOSSConfig()).toEqual(ossConfig);

      // Create new service instance
      const newSettingsService = new SettingsService();
      
      // Configuration should persist
      expect(newSettingsService.getOSSConfig()).toEqual(ossConfig);
      expect(newSettingsService.isOSSConfigured()).toBe(true);
    });
  });

  describe('Storage Type Management', () => {
    it('should manage storage type correctly', () => {
      // Default storage type should be local
      expect(settingsService.getStorageType()).toBe('local');

      // Switch to OSS
      const switchResult = settingsService.setStorageType('oss');
      expect(switchResult).toBe(true);
      expect(settingsService.getStorageType()).toBe('oss');

      // Switch back to local
      const switchBackResult = settingsService.setStorageType('local');
      expect(switchBackResult).toBe(true);
      expect(settingsService.getStorageType()).toBe('local');
    });

    it('should validate storage type during settings validation', () => {
      // Set invalid storage type directly in settings
      const invalidSettings = {
        storageType: 'invalid-type' as any
      };

      settingsService.saveSettings(invalidSettings);
      
      // Should fall back to default
      expect(settingsService.getStorageType()).toBe('local');
    });

    it('should handle OSS storage type without configuration', async () => {
      // Create a completely isolated temp directory for this test
      const isolatedTempDir = path.join(tempDir, 'isolated-test-' + Date.now());
      if (!fs.existsSync(isolatedTempDir)) {
        fs.mkdirSync(isolatedTempDir, { recursive: true });
      }
      
      // Reset module cache to ensure fresh imports
      vi.resetModules();
      
      // Mock app.getPath to use the isolated directory
      vi.doMock('electron', () => ({
        app: {
          getPath: vi.fn().mockReturnValue(isolatedTempDir)
        }
      }));
      
      // Ensure no existing settings file
      const settingsPath = path.join(isolatedTempDir, 'storage-settings.json');
      if (fs.existsSync(settingsPath)) {
        fs.unlinkSync(settingsPath);
      }
      
      // Import SettingsService after mocking
      const { SettingsService: FreshSettingsService } = await import('../../../electron/services/SettingsService');
      
      // Create a fresh service instance in isolated directory
      const freshService = new FreshSettingsService();
      
      // Verify initially no OSS config
      expect(freshService.isOSSConfigured()).toBe(false);
      
      // Try to switch to OSS without configuration
      freshService.setStorageType('oss');
      
      // Should fall back to local due to missing OSS config
      const settings = freshService.getSettings();
      expect(settings.storageType).toBe('local');
      
      // Clean up
      fs.rmSync(isolatedTempDir, { recursive: true, force: true });
    });
  });

  describe('Settings Validation and Recovery', () => {
    it('should recover from corrupted settings file', () => {
      // Create a fresh temp directory for this test
      const testTempDir = path.join(tempDir, 'recovery-test');
      if (!fs.existsSync(testTempDir)) {
        fs.mkdirSync(testTempDir, { recursive: true });
      }
      
      const settingsPath = path.join(testTempDir, 'storage-settings.json');
      
      // Create corrupted settings file
      fs.writeFileSync(settingsPath, 'invalid json content');
      
      // Mock app.getPath to use the specific test directory
      vi.doMock('electron', () => ({
        app: {
          getPath: vi.fn().mockReturnValue(testTempDir)
        }
      }));
      
      // Service should recover with default settings
      const newService = new SettingsService();
      const settings = newService.getSettings();
      
      expect(settings.storageType).toBe('local');
      expect(settings.storagePath).toBeDefined();
      
      // Clean up test directory
      fs.rmSync(testTempDir, { recursive: true, force: true });
    });

    it('should validate and fix incomplete settings', () => {
      // Create a completely isolated temp directory for this test
      const isolatedTempDir = path.join(tempDir, 'incomplete-test-' + Date.now());
      if (!fs.existsSync(isolatedTempDir)) {
        fs.mkdirSync(isolatedTempDir, { recursive: true });
      }
      
      // Mock app.getPath to use the isolated directory
      vi.doMock('electron', () => ({
        app: {
          getPath: vi.fn().mockReturnValue(isolatedTempDir)
        }
      }));
      
      // Create a fresh service instance
      const freshService = new SettingsService();
      
      const incompleteSettings = {
        storagePath: '/some/path',
        // Missing other required fields
      };

      freshService.saveSettings(incompleteSettings);
      const settings = freshService.getSettings();

      // Should fill in missing fields with defaults
      expect(settings.isFirstTimeSetup).toBe(false);
      expect(settings.storageType).toBe('local');
      expect(settings.createdAt).toBeDefined();
      expect(settings.updatedAt).toBeDefined();
      
      // Clean up
      fs.rmSync(isolatedTempDir, { recursive: true, force: true });
    });
  });

  describe('Edge Cases', () => {
    it('should handle OSS configuration with special characters', () => {
      const configWithSpecialChars: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5tFGhjKLMNOPQRSTUVWXYZ',
        secretAccessKey: 'abcdefghijklmnopqrstuvwxyz1234567890ABCDEF',
        bucket: 'my-bucket-with-dashes',
        pathPrefix: 'path/with/slashes'
      };

      expect(settingsService.validateOSSConfig(configWithSpecialChars)).toBe(true);
      
      const saveResult = settingsService.setOSSConfig(configWithSpecialChars);
      expect(saveResult).toBe(true);
      
      const retrievedConfig = settingsService.getOSSConfig();
      expect(retrievedConfig).toEqual(configWithSpecialChars);
    });

    it('should handle very long configuration values', () => {
      const longConfig: OSSConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'LTAI5t' + 'x'.repeat(100),
        secretAccessKey: 'secret' + 'y'.repeat(100),
        bucket: 'bucket' + 'z'.repeat(50),
        pathPrefix: 'prefix/' + 'a'.repeat(100)
      };

      expect(settingsService.validateOSSConfig(longConfig)).toBe(true);
      
      const saveResult = settingsService.setOSSConfig(longConfig);
      expect(saveResult).toBe(true);
      
      const retrievedConfig = settingsService.getOSSConfig();
      expect(retrievedConfig).toEqual(longConfig);
    });
  });
});