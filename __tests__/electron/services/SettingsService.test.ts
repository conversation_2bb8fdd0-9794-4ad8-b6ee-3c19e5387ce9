import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';

// Mock electron模块 - 必须在导入前
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

// Mock fs模块
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
  writeFileSync: vi.fn(),
  mkdirSync: vi.fn(),
  unlinkSync: vi.fn(),
  accessSync: vi.fn(),
  constants: {
    W_OK: 2
  }
}));

// Mock path模块
vi.mock('path', async (importOriginal) => {
  const originalPath = await importOriginal<typeof path>();
  return {
    ...originalPath,
    join: vi.fn((...args) => args.join('/')),
    dirname: vi.fn((filePath) => filePath.split('/').slice(0, -1).join('/')),
    isAbsolute: vi.fn((filePath) => filePath.startsWith('/'))
  };
});

import { SettingsService } from '../../../electron/services/SettingsService';

describe('SettingsService', () => {
  let settingsService: SettingsService;
  let mockSettingsPath: string;

  beforeEach(() => {
    // 重置所有mocks
    vi.clearAllMocks();

    // 设置默认的fs mock行为
    vi.mocked(fs.existsSync).mockReturnValue(false);
    vi.mocked(fs.readFileSync).mockReturnValue('{}');
    vi.mocked(fs.writeFileSync).mockImplementation(() => {});
    vi.mocked(fs.mkdirSync).mockImplementation(() => {});
    vi.mocked(fs.unlinkSync).mockImplementation(() => {});
    vi.mocked(fs.accessSync).mockImplementation(() => {});

    mockSettingsPath = '/tmp/test-userData/storage-settings.json';
    settingsService = new SettingsService();

    // 静默控制台输出
    console.log = vi.fn();
    console.warn = vi.fn();
    console.error = vi.fn();
  });

  describe('构造函数和初始化', () => {
    it('应该成功创建SettingsService实例', () => {
      expect(settingsService).toBeInstanceOf(SettingsService);
    });

    it('应该设置正确的设置文件路径', () => {
      // 验证path.join被正确调用
      expect(path.join).toHaveBeenCalledWith('/tmp/test-userData', 'storage-settings.json');
    });
  });

  describe('getSettings', () => {
    it('应该返回默认设置当设置文件不存在时', () => {
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const settings = settingsService.getSettings();

      expect(settings).toHaveProperty('storagePath');
      expect(settings).toHaveProperty('isFirstTimeSetup', false);
      expect(settings).toHaveProperty('lastMigrationVersion', '1.0.0');
      expect(settings).toHaveProperty('createdAt');
      expect(settings).toHaveProperty('updatedAt');
    });

    it('应该从文件加载设置当文件存在时', () => {
      const testSettings = {
        storagePath: '/custom/storage/path',
        isFirstTimeSetup: true,
        lastMigrationVersion: '2.0.0',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T01:00:00Z'
      };

      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockReturnValue(JSON.stringify(testSettings));

      const settings = settingsService.getSettings();

      expect(settings.storagePath).toBe('/custom/storage/path');
      expect(settings.isFirstTimeSetup).toBe(true);
      expect(settings.lastMigrationVersion).toBe('2.0.0');
    });

    it('应该处理JSON解析错误并返回默认设置', () => {
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockReturnValue('invalid json');

      const settings = settingsService.getSettings();

      expect(settings).toHaveProperty('storagePath');
    });

    it('应该处理文件读取错误并返回默认设置', () => {
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockImplementation(() => {
        throw new Error('File read error');
      });

      const settings = settingsService.getSettings();

      expect(settings).toHaveProperty('storagePath');
    });
  });

  describe('saveSettings', () => {
    it('应该成功保存设置', () => {
      const newSettings = {
        storagePath: '/new/storage/path'
      };

      const result = settingsService.saveSettings(newSettings);

      expect(result).toBe(true);
      expect(fs.writeFileSync).toHaveBeenCalled();
    });

    it('应该更新updatedAt时间戳', () => {
      const newSettings = {
        storagePath: '/new/storage/path'
      };

      settingsService.saveSettings(newSettings);

      // 验证writeFileSync被调用，且包含updatedAt字段
      expect(fs.writeFileSync).toHaveBeenCalled();
      const writeCall = vi.mocked(fs.writeFileSync).mock.calls[0];
      const savedData = JSON.parse(writeCall[1] as string);
      expect(savedData).toHaveProperty('updatedAt');
    });

    it('应该创建设置目录如果不存在', () => {
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const newSettings = {
        storagePath: '/new/storage/path'
      };

      settingsService.saveSettings(newSettings);

      expect(fs.mkdirSync).toHaveBeenCalled();
    });

    it('应该处理保存错误并返回false', () => {
      vi.mocked(fs.writeFileSync).mockImplementation(() => {
        throw new Error('Write error');
      });

      const newSettings = {
        storagePath: '/new/storage/path'
      };

      const result = settingsService.saveSettings(newSettings);

      expect(result).toBe(false);
    });

    it('应该验证并修复无效的设置数据', () => {
      const invalidSettings = {
        storagePath: 'relative/path' // 相对路径，应该被修复
      };

      settingsService.saveSettings(invalidSettings);

      expect(fs.writeFileSync).toHaveBeenCalled();
      const writeCall = vi.mocked(fs.writeFileSync).mock.calls[0];
      const savedData = JSON.parse(writeCall[1] as string);
      
      // 相对路径应该被替换为默认的绝对路径
      expect(savedData.storagePath).toMatch(/^\//); // 应该以/开头（绝对路径）
    });
  });

  describe('isFirstTimeSetup', () => {
    it('应该返回true当设置文件不存在时', () => {
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const result = settingsService.isFirstTimeSetup();

      expect(result).toBe(true);
    });

    it('应该返回true当isFirstTimeSetup为false时', () => {
      const testSettings = {
        storagePath: '/test/path',
        isFirstTimeSetup: false,
        lastMigrationVersion: '1.0.0',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T01:00:00Z'
      };

      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockReturnValue(JSON.stringify(testSettings));

      const result = settingsService.isFirstTimeSetup();

      expect(result).toBe(true);
    });

    it('应该返回false当设置文件存在且isFirstTimeSetup为true时', () => {
      const testSettings = {
        storagePath: '/test/path',
        isFirstTimeSetup: true,
        lastMigrationVersion: '1.0.0',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T01:00:00Z'
      };

      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockReturnValue(JSON.stringify(testSettings));

      const result = settingsService.isFirstTimeSetup();

      expect(result).toBe(false);
    });
  });

  describe('markFirstTimeSetupComplete', () => {
    it('应该标记首次设置已完成', () => {
      const result = settingsService.markFirstTimeSetupComplete();

      expect(result).toBe(true);
      expect(fs.writeFileSync).toHaveBeenCalled();
      
      const writeCall = vi.mocked(fs.writeFileSync).mock.calls[0];
      const savedData = JSON.parse(writeCall[1] as string);
      expect(savedData.isFirstTimeSetup).toBe(true);
    });
  });

  describe('getStoragePath', () => {
    it('应该返回当前存储路径', () => {
      const testSettings = {
        storagePath: '/custom/storage/path',
        isFirstTimeSetup: true,
        lastMigrationVersion: '1.0.0',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T01:00:00Z'
      };

      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockReturnValue(JSON.stringify(testSettings));

      const storagePath = settingsService.getStoragePath();

      expect(storagePath).toBe('/custom/storage/path');
    });
  });

  describe('usesCategoryFolders', () => {
    it('应该始终返回true（已移除配置选项）', () => {
      const testSettings = {
        storagePath: '/test/path',
        isFirstTimeSetup: true,
        lastMigrationVersion: '1.0.0',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T01:00:00Z'
      };

      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockReturnValue(JSON.stringify(testSettings));

      const usesCategoryFolders = settingsService.usesCategoryFolders();

      expect(usesCategoryFolders).toBe(true);
    });
  });

  describe('updateStoragePath', () => {
    it('应该成功更新存储路径', () => {
      vi.mocked(path.isAbsolute).mockReturnValue(true);
      const newPath = '/new/absolute/path';

      const result = settingsService.updateStoragePath(newPath);

      expect(result).toBe(true);
      expect(fs.mkdirSync).toHaveBeenCalledWith(newPath, { recursive: true });
      expect(fs.accessSync).toHaveBeenCalledWith(newPath, fs.constants.W_OK);
      expect(fs.writeFileSync).toHaveBeenCalled();
    });

    it('应该拒绝相对路径', () => {
      vi.mocked(path.isAbsolute).mockReturnValue(false);
      const relativePath = 'relative/path';

      const result = settingsService.updateStoragePath(relativePath);

      expect(result).toBe(false);
      expect(fs.mkdirSync).not.toHaveBeenCalled();
      expect(fs.writeFileSync).not.toHaveBeenCalled();
    });

    it('应该处理目录创建失败', () => {
      vi.mocked(path.isAbsolute).mockReturnValue(true);
      vi.mocked(fs.mkdirSync).mockImplementation(() => {
        throw new Error('Permission denied');
      });

      const newPath = '/new/absolute/path';
      const result = settingsService.updateStoragePath(newPath);

      expect(result).toBe(false);
    });

    it('应该处理目录权限检查失败', () => {
      vi.mocked(path.isAbsolute).mockReturnValue(true);
      vi.mocked(fs.accessSync).mockImplementation(() => {
        throw new Error('Access denied');
      });

      const newPath = '/new/absolute/path';
      const result = settingsService.updateStoragePath(newPath);

      expect(result).toBe(false);
    });

    it('应该跳过目录创建如果目录已存在', () => {
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(path.isAbsolute).mockReturnValue(true); // 确保路径被识别为绝对路径

      const newPath = '/existing/path';
      const result = settingsService.updateStoragePath(newPath);

      expect(result).toBe(true);
      expect(fs.mkdirSync).not.toHaveBeenCalled();
      expect(fs.accessSync).toHaveBeenCalledWith(newPath, fs.constants.W_OK);
    });
  });

  describe('getConfigPath', () => {
    it('应该返回应用配置目录路径', () => {
      const testSettings = {
        storagePath: '/test/storage',
        isFirstTimeSetup: true,
        lastMigrationVersion: '1.0.0',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T01:00:00Z'
      };

      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockReturnValue(JSON.stringify(testSettings));

      const configPath = settingsService.getConfigPath();

      // 检查最后一次调用是否包含我们期望的参数
      const pathJoinCalls = vi.mocked(path.join).mock.calls;
      const lastCall = pathJoinCalls[pathJoinCalls.length - 1];
      expect(lastCall).toEqual(['/test/storage', '.pokedex']);
    });
  });

  describe('ensureConfigDirectory', () => {
    it('应该创建配置目录如果不存在', () => {
      vi.mocked(fs.existsSync).mockReturnValue(false);

      settingsService.ensureConfigDirectory();

      expect(fs.mkdirSync).toHaveBeenCalled();
    });

    it('应该跳过创建如果目录已存在', () => {
      vi.mocked(fs.existsSync).mockReturnValue(true);

      settingsService.ensureConfigDirectory();

      expect(fs.mkdirSync).not.toHaveBeenCalled();
    });
  });

  describe('resetToDefaults', () => {
    it('应该成功重置设置到默认值', () => {
      vi.mocked(fs.existsSync).mockReturnValue(true);

      const result = settingsService.resetToDefaults();

      expect(result).toBe(true);
      expect(fs.unlinkSync).toHaveBeenCalled();
    });

    it('应该处理设置文件不存在的情况', () => {
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const result = settingsService.resetToDefaults();

      expect(result).toBe(true);
      expect(fs.unlinkSync).not.toHaveBeenCalled();
    });

    it('应该处理删除文件失败', () => {
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.unlinkSync).mockImplementation(() => {
        throw new Error('Delete failed');
      });

      const result = settingsService.resetToDefaults();

      expect(result).toBe(false);
    });
  });

  describe('设置验证', () => {
    it('应该修复不完整的设置数据', () => {
      const incompleteSettings = {
        storagePath: '/test/path'
        // 缺少其他字段
      };

      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockReturnValue(JSON.stringify(incompleteSettings));

      const settings = settingsService.getSettings();

      expect(settings).toHaveProperty('isFirstTimeSetup');
      expect(settings).toHaveProperty('lastMigrationVersion');
      expect(settings).toHaveProperty('createdAt');
      expect(settings).toHaveProperty('updatedAt');
    });

    it('应该修复相对路径为绝对路径', () => {
      const settingsWithRelativePath = {
        storagePath: 'relative/path',
        isFirstTimeSetup: true,
        lastMigrationVersion: '1.0.0',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T01:00:00Z'
      };

      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockReturnValue(JSON.stringify(settingsWithRelativePath));
      vi.mocked(path.isAbsolute).mockImplementation((p) => !p.startsWith('relative'));

      const settings = settingsService.getSettings();

      // 应该使用默认的绝对路径
      expect(settings.storagePath).not.toBe('relative/path');
    });
  });
});