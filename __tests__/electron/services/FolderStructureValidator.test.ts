import { FolderStructureValidator } from '../../../electron/services/FolderStructureValidator';
import * as fs from 'fs';
import * as path from 'path';
import { vi, describe, it, expect, beforeEach } from 'vitest';

// Mock fs and path modules
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  statSync: vi.fn(),
  readdirSync: vi.fn(),
}));

vi.mock('path', () => ({
  join: vi.fn(),
  extname: vi.fn(),
}));

describe('FolderStructureValidator', () => {
  let validator: FolderStructureValidator;
  
  beforeEach(() => {
    validator = new FolderStructureValidator();
    vi.clearAllMocks();
  });

  describe('validateFolderStructure', () => {
    it('should return invalid result for non-existent folder', async () => {
      const testFolderPath = '/non/existent/folder';
      
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const result = await validator.validateFolderStructure(testFolderPath);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('文件夹不存在');
    });

    it('should return invalid result for empty folder', async () => {
      const testFolderPath = '/empty/folder';
      
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.statSync).mockReturnValue({
        isDirectory: () => true,
        isFile: () => false,
      } as any);
      vi.mocked(fs.readdirSync).mockReturnValue([]);

      const result = await validator.validateFolderStructure(testFolderPath);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('文件夹为空，没有找到任何分类文件夹');
    });

    it('should return invalid result for folder with no images', async () => {
      const testFolderPath = '/folder/no/images';
      
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.statSync).mockReturnValue({
        isDirectory: () => true,
        isFile: () => false,
      } as any);
      vi.mocked(fs.readdirSync).mockImplementation((dirPath: any) => {
        if (dirPath === testFolderPath) {
          return ['category1'] as any;
        }
        return [] as any; // No images in category folder
      });
      vi.mocked(path.join).mockImplementation((...args) => args.join('/'));

      const result = await validator.validateFolderStructure(testFolderPath);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('分类文件夹 "category1" 中没有找到任何图片');
    });

    it('should validate category name constraints', async () => {
      const testFolderPath = '/test/folder';
      const longName = 'a'.repeat(51); // Exceeds maxCategoryNameLength
      
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(path.join).mockImplementation((...args) => args.join('/'));
      vi.mocked(path.extname).mockImplementation((filepath) => {
        if (filepath.includes('image1.jpg')) return '.jpg';
        return '';
      });
      vi.mocked(fs.readdirSync).mockImplementation((dirPath: any) => {
        if (dirPath === testFolderPath) {
          return [longName] as any;
        }
        return ['image1.jpg'] as any;
      });
      vi.mocked(fs.statSync).mockImplementation((filePath: any) => {
        if (filePath === testFolderPath) {
          return { isDirectory: () => true, isFile: () => false } as any;
        }
        if (filePath.includes(longName)) {
          return { isDirectory: () => true, isFile: () => false } as any;
        }
        return { isDirectory: () => false, isFile: () => true } as any;
      });

      const result = await validator.validateFolderStructure(testFolderPath);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(`分类名称 "${longName}" 超过最大长度限制 (50 字符)`);
    });

    it('should handle file system errors gracefully', async () => {
      const testFolderPath = '/test/folder';
      
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.statSync).mockImplementation(() => {
        throw new Error('Permission denied');
      });

      const result = await validator.validateFolderStructure(testFolderPath);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('文件系统错误: Permission denied');
    });

    it('should return valid result for correct folder structure', async () => {
      const testFolderPath = '/test/folder';
      
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(path.join).mockImplementation((...args) => args.join('/'));
      vi.mocked(path.extname).mockImplementation((filepath) => {
        if (filepath === 'image1.jpg') return '.jpg';
        if (filepath === 'image2.png') return '.png';
        return '';
      });
      
      vi.mocked(fs.statSync).mockImplementation((filePath: any) => {
        if (filePath === testFolderPath) {
          return { isDirectory: () => true, isFile: () => false } as any;
        }
        if (filePath.endsWith('/category1') || filePath.endsWith('/category2')) {
          return { isDirectory: () => true, isFile: () => false } as any;
        }
        if (filePath.includes('image')) {
          return { isDirectory: () => false, isFile: () => true } as any;
        }
        return { isDirectory: () => false, isFile: () => true } as any;
      });
      
      vi.mocked(fs.readdirSync).mockImplementation((dirPath: any) => {
        if (dirPath === testFolderPath) {
          return ['category1', 'category2'] as any;
        }
        return ['image1.jpg', 'image2.png'] as any;
      });

      const result = await validator.validateFolderStructure(testFolderPath);


      expect(result.isValid).toBe(true);
      expect(result.categories).toHaveLength(2);
      expect(result.categories[0].name).toBe('category1');
      expect(result.categories[0].imageCount).toBe(2);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate supported image formats', async () => {
      const testFolderPath = '/test/folder';
      
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(path.join).mockImplementation((...args) => args.join('/'));
      vi.mocked(path.extname).mockImplementation((filepath) => {
        if (filepath === 'image1.jpg') return '.jpg';
        if (filepath === 'image2.png') return '.png';
        if (filepath === 'image3.gif') return '.gif';
        if (filepath === 'image4.bmp') return '.bmp';
        if (filepath === 'image5.webp') return '.webp';
        if (filepath === 'document.txt') return '.txt';
        return '';
      });
      vi.mocked(fs.readdirSync).mockImplementation((dirPath: any) => {
        if (dirPath === testFolderPath) {
          return ['category1'] as any;
        }
        return ['image1.jpg', 'image2.png', 'image3.gif', 'image4.bmp', 'image5.webp', 'document.txt'] as any;
      });
      vi.mocked(fs.statSync).mockImplementation((filePath: any) => {
        if (filePath === testFolderPath) {
          return { isDirectory: () => true, isFile: () => false } as any;
        }
        if (filePath.endsWith('/category1')) {
          return { isDirectory: () => true, isFile: () => false } as any;
        }
        if (filePath.includes('image') || filePath.includes('document')) {
          return { isDirectory: () => false, isFile: () => true } as any;
        }
        return { isDirectory: () => false, isFile: () => true } as any;
      });

      const result = await validator.validateFolderStructure(testFolderPath);

      expect(result.isValid).toBe(true);
      expect(result.categories[0].imageCount).toBe(5);
      expect(result.warnings).toContain('文件 "document.txt" 不是支持的图片格式，将被跳过');
    });
  });

  describe('utility methods', () => {
    it('should check if file format is supported', () => {
      // Mock path.extname for utility method tests
      vi.mocked(path.extname).mockImplementation((filename) => {
        if (filename === 'test.jpg') return '.jpg';
        if (filename === 'test.png') return '.png';
        if (filename === 'test.gif') return '.gif';
        if (filename === 'test.bmp') return '.bmp';
        if (filename === 'test.webp') return '.webp';
        if (filename === 'test.txt') return '.txt';
        if (filename === 'test.pdf') return '.pdf';
        return '';
      });

      expect(validator.isSupportedImageFormat('test.jpg')).toBe(true);
      expect(validator.isSupportedImageFormat('test.png')).toBe(true);
      expect(validator.isSupportedImageFormat('test.gif')).toBe(true);
      expect(validator.isSupportedImageFormat('test.bmp')).toBe(true);
      expect(validator.isSupportedImageFormat('test.webp')).toBe(true);
      expect(validator.isSupportedImageFormat('test.txt')).toBe(false);
      expect(validator.isSupportedImageFormat('test.pdf')).toBe(false);
    });

    it('should return supported formats', () => {
      const formats = validator.getSupportedFormats();
      expect(formats).toContain('.jpg');
      expect(formats).toContain('.png');
      expect(formats).toContain('.gif');
      expect(formats).toContain('.bmp');
      expect(formats).toContain('.webp');
      expect(formats).toHaveLength(6);
    });

    it('should return validation rules', () => {
      const rules = validator.getValidationRules();
      expect(rules.minCategories).toBe(1);
      expect(rules.maxCategories).toBe(100);
      expect(rules.maxCategoryNameLength).toBe(50);
    });
  });
});