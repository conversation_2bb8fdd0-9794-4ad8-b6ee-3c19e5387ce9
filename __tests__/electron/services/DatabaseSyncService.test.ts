import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

// Mock electron模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

// Mock fs模块
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
  writeFileSync: vi.fn(),
  mkdirSync: vi.fn(),
  unlinkSync: vi.fn(),
  accessSync: vi.fn(),
  constants: {
    W_OK: 2
  }
}));

// Mock path模块
vi.mock('path', async (importOriginal) => {
  const originalPath = await importOriginal<typeof path>();
  return {
    ...originalPath,
    join: vi.fn((...args) => args.join('/')),
    dirname: vi.fn((filePath) => filePath.split('/').slice(0, -1).join('/')),
    basename: vi.fn((filePath) => filePath.split('/').pop() || ''),
    extname: vi.fn((filePath) => {
      const parts = filePath.split('.');
      return parts.length > 1 ? '.' + parts.pop() : '';
    })
  };
});

// Mock crypto模块
vi.mock('crypto', () => ({
  createHash: vi.fn(() => ({
    update: vi.fn().mockReturnThis(),
    digest: vi.fn(() => 'mocked-hash-value')
  }))
}));

// Mock better-sqlite3
vi.mock('better-sqlite3', () => ({
  default: vi.fn().mockImplementation(() => ({
    prepare: vi.fn((query: string) => {
      if (query.includes('sqlite_version()')) {
        return {
          get: vi.fn(() => ({ version: '3.36.0' })),
          all: vi.fn(),
          run: vi.fn()
        };
      }
      if (query.includes('sqlite_master')) {
        return {
          get: vi.fn(),
          all: vi.fn(() => [
            { name: 'categories' },
            { name: 'images' },
            { name: 'tags' },
            { name: 'image_tags' }
          ]),
          run: vi.fn()
        };
      }
      if (query.includes('table_info') || query.includes('COUNT(*)')) {
        return {
          get: vi.fn(() => ({ count: 0 })),
          all: vi.fn(() => []),
          run: vi.fn()
        };
      }
      return {
        get: vi.fn(),
        all: vi.fn(),
        run: vi.fn()
      };
    }),
    close: vi.fn(),
    exec: vi.fn()
  }))
}));

import { DatabaseSyncService } from '../../../electron/services/DatabaseSyncService';
import { OSSService } from '../../../electron/services/OSSService';
import { SettingsService } from '../../../electron/services/SettingsService';
import { DatabaseManager } from '../../../electron/database';

// 创建一个模拟的 validateDatabaseStructure 方法
const mockValidateDatabaseStructure = vi.fn();

describe('DatabaseSyncService', () => {
  let databaseSyncService: DatabaseSyncService;
  let mockOSSService: any;
  let mockSettingsService: any;
  let mockDatabaseManager: any;
  let progressCallbackSpy: any;

  beforeEach(() => {
    // 重置所有mocks
    vi.clearAllMocks();
    
    // 重置validateDatabaseStructure mock为默认成功状态
    mockValidateDatabaseStructure.mockResolvedValue({
      isValid: true,
      tableCount: 4,
      tables: ['categories', 'images', 'tags', 'image_tags'],
      sqliteVersion: '3.36.0'
    });

    // 创建Mock服务实例
    mockOSSService = {
      isConfigured: vi.fn(() => true),
      getConfig: vi.fn(() => ({
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket',
        pathPrefix: 'pokedex'
      })),
      uploadFile: vi.fn(),
      downloadFile: vi.fn(),
      listFiles: vi.fn(),
      listFilesWithMetadata: vi.fn(),
      fileExists: vi.fn(),
      updateConfig: vi.fn()
    };

    mockSettingsService = {
      getSettings: vi.fn(() => ({
        lastBackupTime: null,
        lastRestoreTime: null
      })),
      saveSettings: vi.fn(() => true),
      getOSSConfig: vi.fn()
    };

    mockDatabaseManager = {
      getDatabasePath: vi.fn(() => '/tmp/test-db.db'),
      restoreFromBackup: vi.fn(),
      createDatabaseBackup: vi.fn(),
      closeDatabaseSafely: vi.fn()
    };

    progressCallbackSpy = vi.fn();

    // 创建DatabaseSyncService实例
    databaseSyncService = new DatabaseSyncService(
      mockOSSService,
      mockSettingsService,
      mockDatabaseManager
    );

    // Mock 私有方法
    // @ts-ignore - 为了测试目的访问私有方法
    databaseSyncService.validateDatabaseStructure = mockValidateDatabaseStructure;

    // 设置fs.existsSync默认返回true
    vi.mocked(fs.existsSync).mockReturnValue(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('backupDatabaseToOSS', () => {
    it('应该成功备份数据库到OSS', async () => {
      // 准备测试数据（正确的SQLite文件头）
      const mockFileBuffer = Buffer.from('SQLite format 3\0mock database content');
      const expectedBackupName = 'backup-2025-07-17-14-30-45.db';
      
      // 设置mock返回值
      vi.mocked(fs.readFileSync).mockReturnValue(mockFileBuffer);
      mockOSSService.uploadFile.mockResolvedValue({ success: true });
      
      // Mock当前时间以确保可预测的备份名称
      const mockDate = new Date('2025-07-17T14:30:45.000Z');
      vi.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

      // 执行测试
      const result = await databaseSyncService.backupDatabaseToOSS(undefined, progressCallbackSpy);

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.message).toBe('数据库备份成功');
      expect(result.backupInfo).toBeDefined();
      expect(result.backupInfo?.name).toMatch(/^backup-\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.db$/);

      // 验证调用次数和参数
      expect(mockOSSService.uploadFile).toHaveBeenCalledTimes(1);
      expect(mockSettingsService.saveSettings).toHaveBeenCalledWith({
        lastBackupTime: expect.any(String)
      });
      expect(progressCallbackSpy).toHaveBeenCalledTimes(6); // 检查校验 + 10%, 20%, 40%, 60%, 100%
    });

    it('应该在OSS未配置时返回错误', async () => {
      // 设置OSS未配置
      mockOSSService.isConfigured.mockReturnValue(false);

      // 执行测试
      const result = await databaseSyncService.backupDatabaseToOSS();

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS存储，无法执行备份操作');
      expect(mockOSSService.uploadFile).not.toHaveBeenCalled();
    });

    it('应该在数据库文件不存在时返回错误', async () => {
      // 设置数据库文件不存在
      vi.mocked(fs.existsSync).mockReturnValue(false);

      // 执行测试
      const result = await databaseSyncService.backupDatabaseToOSS();

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('数据库文件不存在');
      expect(mockOSSService.uploadFile).not.toHaveBeenCalled();
    });

    it('应该在上传失败时处理重试机制', async () => {
      // 准备测试数据（正确的SQLite文件头）
      const mockFileBuffer = Buffer.from('SQLite format 3\0mock database content');
      
      // 设置mock返回值
      vi.mocked(fs.readFileSync).mockReturnValue(mockFileBuffer);
      
      // 模拟上传失败然后成功
      mockOSSService.uploadFile
        .mockResolvedValueOnce({ success: false, message: 'Network error' })
        .mockResolvedValueOnce({ success: false, message: 'Network error' })
        .mockResolvedValueOnce({ success: true });

      // 执行测试
      const result = await databaseSyncService.backupDatabaseToOSS(undefined, progressCallbackSpy);

      // 验证结果
      expect(result.success).toBe(true);
      expect(mockOSSService.uploadFile).toHaveBeenCalledTimes(3); // 重试了3次
    });

    it('应该在所有重试都失败时返回错误', async () => {
      // 准备测试数据（正确的SQLite文件头）
      const mockFileBuffer = Buffer.from('SQLite format 3\0mock database content');
      
      // 设置mock返回值
      vi.mocked(fs.readFileSync).mockReturnValue(mockFileBuffer);
      mockOSSService.uploadFile.mockResolvedValue({ success: false, message: 'Persistent error' });

      // 执行测试
      const result = await databaseSyncService.backupDatabaseToOSS();

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toContain('备份上传失败');
      expect(mockOSSService.uploadFile).toHaveBeenCalledTimes(3); // 最多重试3次
    });

    it('应该使用自定义备份名称', async () => {
      // 准备测试数据（正确的SQLite文件头）
      const customName = 'custom-backup-name.db';
      const mockFileBuffer = Buffer.from('SQLite format 3\0mock database content');
      
      // 设置mock返回值
      vi.mocked(fs.readFileSync).mockReturnValue(mockFileBuffer);
      mockOSSService.uploadFile.mockResolvedValue({ success: true });

      // 执行测试
      const result = await databaseSyncService.backupDatabaseToOSS(customName);

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.backupInfo?.name).toBe(customName);
      expect(mockOSSService.uploadFile).toHaveBeenCalledWith(
        'databases/custom-backup-name.db',
        expect.any(Buffer),
        'application/x-sqlite3'
      );
    });
  });

  describe('listDatabaseBackups', () => {
    it('应该成功列出数据库备份', async () => {
      // 准备测试数据
      const mockFiles = [
        {
          key: 'databases/backup-2025-07-17-14-30-45.db',
          size: 1024,
          lastModified: '2025-07-17T14:30:45.000Z'
        },
        {
          key: 'databases/backup-2025-07-16-10-15-20.db',
          size: 2048,
          lastModified: '2025-07-16T10:15:20.000Z'
        },
        {
          key: 'databases/not-a-backup.txt',
          size: 100,
          lastModified: '2025-07-15T12:00:00.000Z'
        }
      ];

      // 设置mock返回值
      mockOSSService.listFilesWithMetadata.mockResolvedValue({
        success: true,
        files: mockFiles
      });

      // 执行测试
      const result = await databaseSyncService.listDatabaseBackups();

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.backups).toHaveLength(2); // 只有有效的备份文件
      expect(result.backups?.[0].name).toBe('backup-2025-07-17-14-30-45.db');
      expect(result.backups?.[1].name).toBe('backup-2025-07-16-10-15-20.db');
      expect(result.message).toContain('2 个数据库备份');
    });

    it('应该在OSS未配置时返回错误', async () => {
      // 设置OSS未配置
      mockOSSService.isConfigured.mockReturnValue(false);

      // 执行测试
      const result = await databaseSyncService.listDatabaseBackups();

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS存储，无法获取备份列表');
      expect(mockOSSService.listFilesWithMetadata).not.toHaveBeenCalled();
    });

    it('应该在OSS列表获取失败时返回错误', async () => {
      // 设置OSS列表获取失败
      mockOSSService.listFilesWithMetadata.mockResolvedValue({
        success: false,
        message: 'Access denied'
      });

      // 执行测试
      const result = await databaseSyncService.listDatabaseBackups();

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toContain('获取备份列表失败: Access denied');
    });

    it('应该正确过滤非备份文件', async () => {
      // 准备测试数据（包含各种无效文件）
      const mockFiles = [
        {
          key: 'databases/',
          size: 0,
          lastModified: '2025-07-17T12:00:00.000Z'
        },
        {
          key: 'databases/backup-2025-07-17-14-30-45.db',
          size: 1024,
          lastModified: '2025-07-17T14:30:45.000Z'
        },
        {
          key: 'databases/invalid-name.db',
          size: 512,
          lastModified: '2025-07-16T08:00:00.000Z'
        },
        {
          key: 'databases/backup-invalid-format.txt',
          size: 256,
          lastModified: '2025-07-15T16:00:00.000Z'
        },
        {
          key: 'databases/backup-2025-07-16-10-15-20.db',
          size: 2048,
          lastModified: '2025-07-16T10:15:20.000Z'
        }
      ];

      // 设置mock返回值
      mockOSSService.listFilesWithMetadata.mockResolvedValue({
        success: true,
        files: mockFiles
      });

      // 执行测试
      const result = await databaseSyncService.listDatabaseBackups();

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.backups).toHaveLength(2); // 只有有效的备份文件
      result.backups?.forEach(backup => {
        expect(backup.name).toMatch(/^backup-\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.db$/);
      });
    });
  });

  describe('restoreDatabaseFromOSS', () => {
    it('应该成功从OSS恢复数据库', async () => {
      // 准备测试数据
      const backupName = 'backup-2025-07-17-14-30-45.db';
      const mockBackupData = Buffer.from('SQLite format 3\0mock database content');

      // 设置mock返回值
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });
      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: mockBackupData
      });
      mockDatabaseManager.restoreFromBackup.mockResolvedValue({
        success: true,
        message: '恢复成功'
      });

      // 执行测试
      const result = await databaseSyncService.restoreDatabaseFromOSS(backupName, progressCallbackSpy);

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.message).toBe('数据库恢复成功');
      expect(mockDatabaseManager.restoreFromBackup).toHaveBeenCalledWith(mockBackupData);
      expect(mockSettingsService.saveSettings).toHaveBeenCalledWith({
        lastRestoreTime: expect.any(String)
      });
      expect(progressCallbackSpy).toHaveBeenCalledTimes(6); // 10%, 20%, 60%, 80%, 90%, 100%
    });

    it('应该在OSS未配置时返回错误', async () => {
      // 设置OSS未配置
      mockOSSService.isConfigured.mockReturnValue(false);

      // 执行测试
      const result = await databaseSyncService.restoreDatabaseFromOSS('backup-test.db');

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS存储，无法执行恢复操作');
      expect(mockOSSService.downloadFile).not.toHaveBeenCalled();
    });

    it('应该在备份文件不存在时返回错误', async () => {
      // 设置备份文件不存在
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: false
      });

      // 执行测试
      const result = await databaseSyncService.restoreDatabaseFromOSS('non-existent-backup.db');

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('指定的备份文件不存在');
      expect(mockOSSService.downloadFile).not.toHaveBeenCalled();
    });

    it('应该在下载失败时处理重试机制', async () => {
      // 准备测试数据
      const backupName = 'backup-2025-07-17-14-30-45.db';
      const mockBackupData = Buffer.from('SQLite format 3\0mock database content');

      // 设置mock返回值
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });
      
      // 模拟下载失败然后成功
      mockOSSService.downloadFile
        .mockResolvedValueOnce({ success: false, message: 'Network error' })
        .mockResolvedValueOnce({ success: false, message: 'Network error' })
        .mockResolvedValueOnce({ success: true, data: mockBackupData });

      mockDatabaseManager.restoreFromBackup.mockResolvedValue({
        success: true,
        message: '恢复成功'
      });

      // 执行测试
      const result = await databaseSyncService.restoreDatabaseFromOSS(backupName, progressCallbackSpy);

      // 验证结果
      expect(result.success).toBe(true);
      expect(mockOSSService.downloadFile).toHaveBeenCalledTimes(3); // 重试了3次
    });

    it('应该在备份数据验证失败时返回错误', async () => {
      // 准备测试数据（无效的SQLite文件）
      const backupName = 'backup-2025-07-17-14-30-45.db';
      const mockInvalidData = Buffer.from('Invalid database content');

      // 设置mock返回值
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });
      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: mockInvalidData
      });

      // 执行测试
      const result = await databaseSyncService.restoreDatabaseFromOSS(backupName);

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toContain('备份数据验证失败');
      expect(mockDatabaseManager.restoreFromBackup).not.toHaveBeenCalled();
    });
  });

  describe('validateDatabaseBackup', () => {
    it('应该成功验证有效的数据库备份', async () => {
      // 准备测试数据
      const backupName = 'backup-2025-07-17-14-30-45.db';
      const mockValidData = Buffer.from('SQLite format 3\0mock database content');

      // 设置mock返回值
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });
      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: mockValidData
      });

      // 模拟验证方法
      const mockValidateBackup = vi.fn().mockResolvedValue({
        success: true,
        valid: true,
        message: '备份文件验证通过',
        details: {
          fileSize: mockValidData.length,
          isValidSQLite: true,
          hasRequiredTables: true
        }
      });
      (databaseSyncService as any).validateDatabaseBackup = mockValidateBackup;

      // 执行测试
      const result = await databaseSyncService.validateDatabaseBackup(backupName);

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.valid).toBe(true);
      expect(result.message).toBe('备份文件验证通过');
      expect(result.details).toBeDefined();
    });

    it('应该在OSS未配置时返回错误', async () => {
      // 设置OSS未配置
      mockOSSService.isConfigured.mockReturnValue(false);

      // 执行测试
      const result = await databaseSyncService.validateDatabaseBackup('backup-test.db');

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('未配置OSS存储');
    });

    it('应该在备份文件不存在时返回无效', async () => {
      // 设置备份文件不存在
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: false
      });

      // 执行测试
      const result = await databaseSyncService.validateDatabaseBackup('non-existent.db');

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('备份文件不存在');
    });

    it('应该在无效SQLite文件时返回无效', async () => {
      // 准备测试数据（无效的SQLite文件）
      const backupName = 'backup-2025-07-17-14-30-45.db';
      const mockInvalidData = Buffer.from('Invalid database content');

      // 设置mock返回值
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });
      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: mockInvalidData
      });

      // 执行测试
      const result = await databaseSyncService.validateDatabaseBackup(backupName);

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('备份文件格式无效，不是有效的SQLite数据库');
    });
  });

  describe('getSyncStatus', () => {
    it('应该返回正确的同步状态', () => {
      // 准备测试数据
      const mockSettings = {
        lastBackupTime: '2025-07-17T14:30:45.000Z',
        lastRestoreTime: '2025-07-16T10:15:20.000Z'
      };
      mockSettingsService.getSettings.mockReturnValue(mockSettings);

      // 执行测试
      const status = databaseSyncService.getSyncStatus();

      // 验证结果
      expect(status.canSync).toBe(true);
      expect(status.lastBackupTime).toBe(mockSettings.lastBackupTime);
      expect(status.lastRestoreTime).toBe(mockSettings.lastRestoreTime);
    });

    it('应该在OSS未配置时返回无法同步', () => {
      // 设置OSS未配置
      mockOSSService.isConfigured.mockReturnValue(false);

      // 执行测试
      const status = databaseSyncService.getSyncStatus();

      // 验证结果
      expect(status.canSync).toBe(false);
    });
  });

  describe('canSync', () => {
    it('应该在OSS已配置时返回true', () => {
      // 执行测试
      const canSync = databaseSyncService.canSync();

      // 验证结果
      expect(canSync).toBe(true);
      expect(mockOSSService.isConfigured).toHaveBeenCalled();
    });

    it('应该在OSS未配置时返回false', () => {
      // 设置OSS未配置
      mockOSSService.isConfigured.mockReturnValue(false);

      // 执行测试
      const canSync = databaseSyncService.canSync();

      // 验证结果
      expect(canSync).toBe(false);
    });
  });

  describe('错误处理', () => {
    it('应该处理备份过程中的异常', async () => {
      // 设置mock抛出异常
      mockDatabaseManager.getDatabasePath.mockImplementation(() => {
        throw new Error('Database path error');
      });

      // 执行测试
      const result = await databaseSyncService.backupDatabaseToOSS();

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toContain('备份失败: Database path error');
    });

    it('应该处理恢复过程中的异常', async () => {
      // 设置mock抛出异常
      mockOSSService.fileExists.mockImplementation(() => {
        throw new Error('OSS connection error');
      });

      // 执行测试
      const result = await databaseSyncService.restoreDatabaseFromOSS('test-backup.db');

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toContain('数据库恢复失败: OSS connection error');
    });

    it('应该处理列表获取过程中的异常', async () => {
      // 设置mock抛出异常
      mockOSSService.listFilesWithMetadata.mockImplementation(() => {
        throw new Error('Network timeout');
      });

      // 执行测试
      const result = await databaseSyncService.listDatabaseBackups();

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toContain('获取备份列表失败: Network timeout');
    });
  });
});