import { BatchImportService, ImportMode } from '../../../electron/services/BatchImportService';
import { FolderStructureValidator } from '../../../electron/services/FolderStructureValidator';
import { DatabaseManager } from '../../../electron/database';
import { CategoryService } from '../../../electron/services/CategoryService';
import { ImageService } from '../../../electron/services/ImageService';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

// Mock all dependencies
vi.mock('../../../electron/services/FolderStructureValidator');
vi.mock('../../../electron/database');
vi.mock('../../../electron/services/CategoryService');
vi.mock('../../../electron/services/ImageService');
vi.mock('fs');
vi.mock('path');
vi.mock('util');

describe('BatchImportService', () => {
  let batchImportService: BatchImportService;
  let mockDatabaseManager: any;
  let mockCategoryService: any;
  let mockImageService: any;
  let mockFolderValidator: any;

  beforeEach(() => {
    // Create mock instances
    mockDatabaseManager = {
      getDatabase: vi.fn(),
      resetDatabase: vi.fn(),
      clearDatabase: vi.fn(),
    };
    
    mockCategoryService = {
      createCategory: vi.fn(),
      getCategories: vi.fn(),
    };
    
    mockImageService = {
      uploadImage: vi.fn(),
    };
    
    mockFolderValidator = {
      validateFolderStructure: vi.fn(),
      isSupportedImageFormat: vi.fn(),
    };

    // Mock constructors
    vi.mocked(DatabaseManager).mockImplementation(() => mockDatabaseManager);
    vi.mocked(CategoryService).mockImplementation(() => mockCategoryService);
    vi.mocked(ImageService).mockImplementation(() => mockImageService);
    vi.mocked(FolderStructureValidator).mockImplementation(() => mockFolderValidator);

    // Mock promisify to return proper async fs methods
    vi.mocked(promisify).mockImplementation((fn: any) => {
      if (fn === fs.readFile) {
        return vi.fn().mockResolvedValue(Buffer.from('fake-image-data'));
      }
      if (fn === fs.readdir) {
        return vi.fn().mockImplementation((dirPath: string) => {
          if (dirPath.includes('birds')) {
            return Promise.resolve(['bird1.jpg', 'bird2.png']);
          }
          if (dirPath.includes('flowers')) {
            return Promise.resolve(['flower1.jpg']);
          }
          return Promise.resolve([]);
        });
      }
      if (fn === fs.stat) {
        return vi.fn().mockResolvedValue({ size: 1024 * 1024 }); // 1MB
      }
      return fn;
    });

    batchImportService = new BatchImportService(mockDatabaseManager);
    
    vi.clearAllMocks();
  });

  describe('importFromFolder', () => {
    it('should successfully import from a valid folder structure', async () => {
      const testFolderPath = '/test/folder';
      const options = {
        importMode: ImportMode.OVERWRITE,
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock validation result
      mockFolderValidator.validateFolderStructure.mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 2, path: '/test/folder/birds' },
          { name: 'flowers', imageCount: 1, path: '/test/folder/flowers' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock database reset
      mockDatabaseManager.clearDatabase.mockResolvedValue({ success: true });

      // Mock category creation
      mockCategoryService.createCategory.mockResolvedValue({ id: 'category-id' });

      // Mock file system operations
      vi.mocked(fs.readdirSync).mockImplementation((dirPath) => {
        if (dirPath === '/test/folder/birds') {
          return ['bird1.jpg', 'bird2.png'] as any;
        }
        if (dirPath === '/test/folder/flowers') {
          return ['flower1.jpg'] as any;
        }
        return [] as any;
      });

      vi.mocked(fs.readFileSync).mockReturnValue(Buffer.from('fake-image-data'));
      vi.mocked(path.join).mockImplementation((...args) => args.join('/'));
      vi.mocked(path.extname).mockImplementation((filepath) => {
        if (filepath.includes('.jpg')) return '.jpg';
        if (filepath.includes('.png')) return '.png';
        return '';
      });

      // Mock folder validator methods
      mockFolderValidator.isSupportedImageFormat.mockReturnValue(true);

      // Mock image upload - returns ImageRead object
      mockImageService.uploadImage.mockResolvedValue({ 
        id: 'image-id',
        original_filename: 'test.jpg',
        stored_filename: 'stored.jpg',
        file_size: 1024,
        mime_type: 'image/jpeg',
        width: 800,
        height: 600,
        category_id: 'category-id',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const progressCallback = vi.fn();
      const result = await batchImportService.importFromFolder(
        testFolderPath,
        options,
        progressCallback
      );

      expect(result.success).toBe(true);
      expect(result.totalCategories).toBe(2);
      expect(result.importedCategories).toBe(2);
      expect(result.totalImages).toBe(3);
      expect(result.importedImages).toBe(3);
      expect(result.failedImages).toBe(0);
      expect(result.errors).toHaveLength(0);

      // Verify database was reset
      expect(mockDatabaseManager.clearDatabase).toHaveBeenCalled();

      // Verify categories were created
      expect(mockCategoryService.createCategory).toHaveBeenCalledTimes(2);
      expect(mockCategoryService.createCategory).toHaveBeenCalledWith({
        name: 'birds',
        description: null,
      });
      expect(mockCategoryService.createCategory).toHaveBeenCalledWith({
        name: 'flowers',
        description: null,
      });

      // Verify images were uploaded
      expect(mockImageService.uploadImage).toHaveBeenCalledTimes(3);

      // Verify progress callback was called
      expect(progressCallback).toHaveBeenCalled();
    });

    it('should fail when folder validation fails', async () => {
      const testFolderPath = '/invalid/folder';
      const options = {
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock validation failure
      mockFolderValidator.validateFolderStructure.mockResolvedValue({
        isValid: false,
        categories: [],
        errors: ['文件夹不存在'],
        warnings: [],
      });

      const progressCallback = vi.fn();
      const result = await batchImportService.importFromFolder(
        testFolderPath,
        options,
        progressCallback
      );

      expect(result.success).toBe(false);
      expect(result.errors).toContain('文件夹结构验证失败: 文件夹不存在');
      expect(mockDatabaseManager.resetDatabase).not.toHaveBeenCalled();
    });

    it('should handle database reset failure', async () => {
      const testFolderPath = '/test/folder';
      const options = {
        importMode: ImportMode.OVERWRITE,
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock validation success
      mockFolderValidator.validateFolderStructure.mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 1, path: '/test/folder/birds' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock database reset failure
      mockDatabaseManager.clearDatabase.mockResolvedValue({
        success: false,
        message: 'Database reset failed'
      });

      const progressCallback = vi.fn();
      const result = await batchImportService.importFromFolder(
        testFolderPath,
        options,
        progressCallback
      );

      expect(result.success).toBe(false);
      expect(result.errors).toContain('数据库清空失败: Database reset failed');
      expect(mockCategoryService.createCategory).not.toHaveBeenCalled();
    });

    it('should handle category creation failure', async () => {
      const testFolderPath = '/test/folder';
      const options = {
        importMode: ImportMode.OVERWRITE,
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock validation success
      mockFolderValidator.validateFolderStructure.mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 1, path: '/test/folder/birds' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock database reset success
      mockDatabaseManager.clearDatabase.mockResolvedValue({ success: true });

      // Mock category creation failure
      mockCategoryService.createCategory.mockRejectedValue(new Error('Category creation failed'));

      const progressCallback = vi.fn();
      const result = await batchImportService.importFromFolder(
        testFolderPath,
        options,
        progressCallback
      );

      expect(result.success).toBe(false);
      expect(result.errors).toContain('创建分类 "birds" 失败: Category creation failed');
    });

    it('should handle image upload failure gracefully', async () => {
      const testFolderPath = '/test/folder';
      const options = {
        importMode: ImportMode.OVERWRITE,
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock validation success
      mockFolderValidator.validateFolderStructure.mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 2, path: '/test/folder/birds' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock database reset success
      mockDatabaseManager.clearDatabase.mockResolvedValue({ success: true });

      // Mock category creation success
      mockCategoryService.createCategory.mockResolvedValue({ id: 'category-id' });

      // Mock file system operations
      vi.mocked(fs.readdirSync).mockReturnValue(['bird1.jpg', 'bird2.png'] as any);
      vi.mocked(fs.readFileSync).mockReturnValue(Buffer.from('fake-image-data'));
      vi.mocked(path.join).mockImplementation((...args) => args.join('/'));
      vi.mocked(path.extname).mockImplementation((filepath) => {
        if (filepath.includes('.jpg')) return '.jpg';
        if (filepath.includes('.png')) return '.png';
        return '';
      });

      // Mock folder validator methods
      mockFolderValidator.isSupportedImageFormat.mockReturnValue(true);

      // Mock image upload - first succeeds, second fails
      mockImageService.uploadImage
        .mockResolvedValueOnce({ 
          id: 'image-1',
          original_filename: 'bird1.jpg',
          stored_filename: 'stored1.jpg',
          file_size: 1024,
          mime_type: 'image/jpeg',
          width: 800,
          height: 600,
          category_id: 'category-id',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .mockRejectedValueOnce(new Error('Upload failed'));

      const progressCallback = vi.fn();
      const result = await batchImportService.importFromFolder(
        testFolderPath,
        options,
        progressCallback
      );

      expect(result.success).toBe(true); // Should still succeed overall
      expect(result.totalImages).toBe(2);
      expect(result.importedImages).toBe(1);
      expect(result.failedImages).toBe(1);
      expect(result.warnings).toContain('图片上传失败: bird2.png - Upload failed');
    });
  });

  describe('progress reporting', () => {
    it('should report progress during import', async () => {
      const testFolderPath = '/test/folder';
      const options = {
        importMode: ImportMode.OVERWRITE,
        overwriteExisting: true,
        compressImages: false,
        compressionQuality: 80,
        sanitizeCategoryNames: false,
        createThumbnails: true,
        preserveFileNames: true,
      };

      // Mock validation success
      mockFolderValidator.validateFolderStructure.mockResolvedValue({
        isValid: true,
        categories: [
          { name: 'birds', imageCount: 1, path: '/test/folder/birds' },
        ],
        errors: [],
        warnings: [],
      });

      // Mock other operations
      mockDatabaseManager.clearDatabase.mockResolvedValue({ success: true });
      mockCategoryService.createCategory.mockResolvedValue({ id: 'category-id' });
      
      vi.mocked(fs.readdirSync).mockReturnValue(['bird1.jpg'] as any);
      vi.mocked(fs.readFileSync).mockReturnValue(Buffer.from('fake-image-data'));
      vi.mocked(path.join).mockImplementation((...args) => args.join('/'));
      vi.mocked(path.extname).mockReturnValue('.jpg');

      // Mock folder validator methods
      mockFolderValidator.isSupportedImageFormat.mockReturnValue(true);

      mockImageService.uploadImage.mockResolvedValue({ 
        id: 'image-id',
        original_filename: 'bird1.jpg',
        stored_filename: 'stored.jpg',
        file_size: 1024,
        mime_type: 'image/jpeg',
        width: 800,
        height: 600,
        category_id: 'category-id',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const progressCallback = vi.fn();
      await batchImportService.importFromFolder(
        testFolderPath,
        options,
        progressCallback
      );

      // Verify progress was reported for different stages
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: 'validation',
          progress: expect.any(Number),
        })
      );
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: 'validation',
          progress: expect.any(Number),
        })
      );
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: 'categories',
          progress: expect.any(Number),
        })
      );
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: 'images',
          progress: expect.any(Number),
        })
      );
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: 'complete',
          progress: 100,
        })
      );
    });
  });
});