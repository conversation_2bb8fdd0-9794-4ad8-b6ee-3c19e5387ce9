/**
 * @vitest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { <PERSON>rowserRouter } from 'react-router-dom';
import Layout from '../../components/Layout';
import { AuthProvider } from '../../contexts/AuthContext';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { CategoryProvider } from '../../contexts/CategoryContext';

// Mock the constants
vi.mock('../../constants', () => ({
  API_BASE_URL: '/',
  IMAGE_BASE_URL: 'http://test.com',
  MAX_CATEGORIES_TO_LOAD_IMAGES_FROM: 2000,
  IS_ELECTRON: true,
  IS_PWA_ENVIRONMENT: false,
  PLATFORM: 'test',
  isElectronEnvironment: () => true,
  isPWAEnvironment: () => false,
  getPlatform: () => 'test',
}));

// Mock the API services
vi.mock('../../services/api', () => ({
  getCategories: vi.fn().mockResolvedValue([]),
}));

// Mock the electron API
const mockElectronAPI = {
  selectImportFolder: vi.fn(),
  validateFolderStructure: vi.fn(),
  importFromFolder: vi.fn(),
  onImportProgress: vi.fn(),
  resetDatabase: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock window.electronAPI
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

// Mock window.confirm
Object.defineProperty(window, 'confirm', {
  value: vi.fn(),
  writable: true,
});

// Mock window.alert
Object.defineProperty(window, 'alert', {
  value: vi.fn(),
  writable: true,
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
  writable: true,
});

// Mock window.location.reload
const mockReload = vi.fn();
Object.defineProperty(window, 'location', {
  value: {
    ...window.location,
    reload: mockReload,
  },
  writable: true,
});

// Mock console.log and console.error
const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      <ThemeProvider>
        <CategoryProvider>
          {children}
        </CategoryProvider>
      </ThemeProvider>
    </AuthProvider>
  </BrowserRouter>
);

describe('Layout Import Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    consoleSpy.mockClear();
    consoleErrorSpy.mockClear();
  });

  afterEach(() => {
    cleanup();
  });

  it('should render import button in Electron environment', () => {
    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');
    expect(importButton).toBeInTheDocument();
    expect(importButton).toHaveTextContent('导入');
  });

  it('should start import process when import button is clicked', async () => {
    const mockFolderPath = '/test/folder';
    const mockValidationResult = {
      isValid: true,
      categories: [
        { name: 'birds', imageCount: 3 },
        { name: 'flowers', imageCount: 2 },
      ],
      errors: [],
      warnings: [],
    };
    const mockImportResult = {
      success: true,
      totalCategories: 2,
      importedCategories: 2,
      totalImages: 5,
      importedImages: 5,
      failedImages: 0,
      errors: [],
      warnings: [],
    };

    // Mock the electron API calls
    mockElectronAPI.selectImportFolder.mockResolvedValue({ filePath: mockFolderPath });
    mockElectronAPI.validateFolderStructure.mockResolvedValue(mockValidationResult);
    mockElectronAPI.importFromFolder.mockResolvedValue(mockImportResult);

    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');

    // Click the import button
    fireEvent.click(importButton);

    // Wait for the import process to complete
    await waitFor(() => {
      expect(mockElectronAPI.selectImportFolder).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(mockElectronAPI.validateFolderStructure).toHaveBeenCalledWith(mockFolderPath);
    });

    // Wait for the mode selection dialog to appear
    await waitFor(() => {
      expect(screen.getAllByText('选择导入模式')[0]).toBeInTheDocument();
    });

    // Click the "确定导入" button to confirm the selection
    const confirmButton = screen.getAllByText('确定导入')[0];
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockElectronAPI.importFromFolder).toHaveBeenCalledWith(
        mockFolderPath,
        expect.objectContaining({
          importMode: 'overwrite',
          overwriteExisting: false,
          compressImages: false,
          compressionQuality: 80,
          sanitizeCategoryNames: false,
          createThumbnails: true,
          preserveFileNames: true,
        })
      );
    });
  });

  it('should handle folder selection cancellation', async () => {
    // Mock user cancelling folder selection
    mockElectronAPI.selectImportFolder.mockResolvedValue(null);

    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');
    
    // Click the import button
    fireEvent.click(importButton);

    // Wait for the folder selection to complete
    await waitFor(() => {
      expect(mockElectronAPI.selectImportFolder).toHaveBeenCalled();
    });

    // Should not proceed with validation
    expect(mockElectronAPI.validateFolderStructure).not.toHaveBeenCalled();
  });

  it('should handle folder validation failure', async () => {
    const mockFolderPath = '/invalid/folder';
    const mockValidationResult = {
      isValid: false,
      categories: [],
      errors: ['文件夹不存在', '格式不正确'],
      warnings: [],
    };

    mockElectronAPI.selectImportFolder.mockResolvedValue({ filePath: mockFolderPath });
    mockElectronAPI.validateFolderStructure.mockResolvedValue(mockValidationResult);

    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');
    
    // Click the import button
    fireEvent.click(importButton);

    // Wait for the validation to complete
    await waitFor(() => {
      expect(mockElectronAPI.validateFolderStructure).toHaveBeenCalledWith(mockFolderPath);
    });

    // Should show error alert
    await waitFor(() => {
      expect(window.alert).toHaveBeenCalledWith(
        expect.stringContaining('文件夹结构不符合要求')
      );
    });

    // Should not proceed with import
    expect(mockElectronAPI.importFromFolder).not.toHaveBeenCalled();
  });

  it('should handle user cancelling import mode selection', async () => {
    const mockFolderPath = '/test/folder';
    const mockValidationResult = {
      isValid: true,
      categories: [
        { name: 'birds', imageCount: 3 },
      ],
      errors: [],
      warnings: [],
    };

    mockElectronAPI.selectImportFolder.mockResolvedValue({ filePath: mockFolderPath });
    mockElectronAPI.validateFolderStructure.mockResolvedValue(mockValidationResult);

    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');

    // Click the import button
    fireEvent.click(importButton);

    // Wait for the mode selection dialog to appear
    await waitFor(() => {
      expect(screen.getAllByText('选择导入模式')[0]).toBeInTheDocument();
    });

    // Click cancel button
    const cancelButton = screen.getAllByText('取消')[0];
    fireEvent.click(cancelButton);

    // Wait for dialog to close
    await waitFor(() => {
      expect(screen.queryByText('选择导入模式')).not.toBeInTheDocument();
    });

    // Should not proceed with import
    expect(mockElectronAPI.importFromFolder).not.toHaveBeenCalled();
  });

  it('should handle import failure', async () => {
    const mockFolderPath = '/test/folder';
    const mockValidationResult = {
      isValid: true,
      categories: [
        { name: 'birds', imageCount: 3 },
      ],
      errors: [],
      warnings: [],
    };
    const mockImportResult = {
      success: false,
      totalCategories: 1,
      importedCategories: 0,
      totalImages: 3,
      importedImages: 0,
      failedImages: 3,
      errors: ['数据库错误', '文件读取失败'],
      warnings: [],
    };

    mockElectronAPI.selectImportFolder.mockResolvedValue({ filePath: mockFolderPath });
    mockElectronAPI.validateFolderStructure.mockResolvedValue(mockValidationResult);
    mockElectronAPI.importFromFolder.mockResolvedValue(mockImportResult);

    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');

    // Click the import button
    fireEvent.click(importButton);

    // Wait for the mode selection dialog to appear
    await waitFor(() => {
      expect(screen.getAllByText('选择导入模式')[0]).toBeInTheDocument();
    });

    // Click the "确定导入" button to confirm the selection
    const confirmButton = screen.getAllByText('确定导入')[0];
    fireEvent.click(confirmButton);

    // Wait for the import process to complete
    await waitFor(() => {
      expect(mockElectronAPI.importFromFolder).toHaveBeenCalled();
    });

    // Should show error alert
    await waitFor(() => {
      expect(window.alert).toHaveBeenCalledWith(
        expect.stringContaining('导入失败')
      );
    });

    // Should not reload the page
    expect(mockReload).not.toHaveBeenCalled();
  });

  it('should handle successful import with progress monitoring', async () => {
    const mockFolderPath = '/test/folder';
    const mockValidationResult = {
      isValid: true,
      categories: [
        { name: 'birds', imageCount: 3 },
        { name: 'flowers', imageCount: 2 },
      ],
      errors: [],
      warnings: [],
    };
    const mockImportResult = {
      success: true,
      totalCategories: 2,
      importedCategories: 2,
      totalImages: 5,
      importedImages: 5,
      failedImages: 0,
      errors: [],
      warnings: [],
    };

    mockElectronAPI.selectImportFolder.mockResolvedValue({ filePath: mockFolderPath });
    mockElectronAPI.validateFolderStructure.mockResolvedValue(mockValidationResult);
    mockElectronAPI.importFromFolder.mockResolvedValue(mockImportResult);
    
    // Mock progress handler
    let progressHandler: ((progress: any) => void) | undefined;
    mockElectronAPI.onImportProgress.mockImplementation((handler) => {
      progressHandler = handler;
    });

    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');

    // Click the import button
    fireEvent.click(importButton);

    // Wait for the mode selection dialog to appear
    await waitFor(() => {
      expect(screen.getAllByText('选择导入模式')[0]).toBeInTheDocument();
    });

    // Click the "确定导入" button to confirm the selection
    const confirmButton = screen.getAllByText('确定导入')[0];
    fireEvent.click(confirmButton);

    // Wait for progress handler to be set
    await waitFor(() => {
      expect(mockElectronAPI.onImportProgress).toHaveBeenCalled();
    });

    // Simulate progress updates
    if (progressHandler) {
      progressHandler({ stage: 'validation', progress: 10 });
      progressHandler({ stage: 'reset', progress: 20 });
      progressHandler({ stage: 'categories', progress: 50 });
      progressHandler({ stage: 'images', progress: 80 });
      progressHandler({ stage: 'complete', progress: 100 });
    }

    // Wait for the import process to complete
    await waitFor(() => {
      expect(mockElectronAPI.importFromFolder).toHaveBeenCalled();
    });

    // Should show success alert
    await waitFor(() => {
      expect(window.alert).toHaveBeenCalledWith(
        expect.stringContaining('导入成功')
      );
    });

    // Should reload the page
    await waitFor(() => {
      expect(mockReload).toHaveBeenCalled();
    });

    // Should log progress updates
    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('导入进度: 10% - validation')
    );
    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('导入进度: 100% - complete')
    );
  });

  it('should handle errors during import process', async () => {
    const mockFolderPath = '/test/folder';
    const mockError = new Error('Import failed');

    mockElectronAPI.selectImportFolder.mockResolvedValue({ filePath: mockFolderPath });
    mockElectronAPI.validateFolderStructure.mockRejectedValue(mockError);

    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');
    
    // Click the import button
    fireEvent.click(importButton);

    // Wait for the error to be handled
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '文件夹导入失败:', mockError
      );
    });

    // Should show error alert
    await waitFor(() => {
      expect(window.alert).toHaveBeenCalledWith(
        expect.stringContaining('文件夹导入失败')
      );
    });
  });

  it('should disable import button during import process', async () => {
    const mockFolderPath = '/test/folder';
    
    // Mock a long-running import process
    mockElectronAPI.selectImportFolder.mockImplementation(() =>
      new Promise(resolve => setTimeout(() => resolve({ filePath: mockFolderPath }), 100))
    );

    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');
    
    // Click the import button
    fireEvent.click(importButton);

    // Button should be disabled during import
    expect(importButton).toBeDisabled();
    expect(importButton).toHaveClass('opacity-50');
    expect(importButton).toHaveClass('cursor-not-allowed');
    
    // Wait for the process to complete
    await waitFor(() => {
      expect(mockElectronAPI.selectImportFolder).toHaveBeenCalled();
    });

    // Button should be enabled again
    await waitFor(() => {
      expect(importButton).not.toBeDisabled();
    });
  });

  it('should show loading state during import', async () => {
    const mockFolderPath = '/test/folder';
    
    // Mock a long-running import process
    mockElectronAPI.selectImportFolder.mockImplementation(() =>
      new Promise(resolve => setTimeout(() => resolve({ filePath: mockFolderPath }), 100))
    );

    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    const importButton = screen.getByTitle('从文件夹导入');
    
    // Click the import button
    fireEvent.click(importButton);

    // Should show loading text
    expect(importButton).toHaveTextContent('导入中...');
    
    // Should have pulse animation
    const icon = importButton.querySelector('svg');
    expect(icon).toHaveClass('animate-pulse');
    
    // Wait for the process to complete
    await waitFor(() => {
      expect(mockElectronAPI.selectImportFolder).toHaveBeenCalled();
    });

    // Should show normal text again
    await waitFor(() => {
      expect(importButton).toHaveTextContent('导入');
    });
  });
});