import { beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { setupElectronMocks, cleanupElectronMocks } from '../electron/helpers/electron-mocks';
import { TestDatabaseManager } from '../electron/helpers/test-database';
import '@testing-library/jest-dom';

// 全局测试设置
beforeAll(() => {
  // 设置Electron mocks
  setupElectronMocks();
  
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  
  // 静默console.log输出以减少测试噪音（在组件测试中会被spy覆盖）
  // console.log = () => {};
});

afterAll(() => {
  // 清理所有测试数据库
  TestDatabaseManager.getInstance().cleanupAll();
  
  // 清理Electron mocks
  cleanupElectronMocks();
});

// 每个测试前的清理
beforeEach(() => {
  // 清理所有mock的调用历史
  vi.clearAllMocks();
});

afterEach(() => {
  // 每个测试后的清理工作可以在这里添加
});

// 全局测试工具函数
declare global {
  var testUtils: {
    delay: (ms: number) => Promise<void>;
    expectAsync: <T>(fn: () => Promise<T>) => Promise<T>;
  };
}

globalThis.testUtils = {
  // 延迟工具函数
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 异步期望工具函数
  expectAsync: async <T>(fn: () => Promise<T>): Promise<T> => {
    try {
      return await fn();
    } catch (error) {
      throw error;
    }
  }
};