import Database from 'better-sqlite3';
import { app } from 'electron';
import path from 'path';
import fs from 'fs';

export interface DatabaseMigrationOptions {
  onProgress?: (info: { stage: string; progress: number }) => void;
}

export class DatabaseManager {
  private db: Database.Database;
  private dbPath: string;
  
  constructor(customDbPath?: string, options?: DatabaseMigrationOptions) {
    if (customDbPath) {
      this.dbPath = customDbPath;
    } else {
      const userDataPath = app.getPath('userData');
      this.dbPath = path.join(userDataPath, 'database.db');
    }
    
    console.log('SQLite数据库路径:', this.dbPath);
    
    // 执行数据库迁移逻辑
    this.performDatabaseMigration(options);
    
    // 确保目录存在
    fs.mkdirSync(path.dirname(this.dbPath), { recursive: true });
    
    this.db = new Database(this.dbPath);
    this.initDatabase();
  }

  /**
   * 执行数据库迁移逻辑
   */
  private performDatabaseMigration(options?: DatabaseMigrationOptions) {
    const newDbPath = this.dbPath;
    const oldDbPath = path.join(app.getPath('userData'), 'database.db');
    
    // 报告进度：开始检查
    if (options?.onProgress) {
      options.onProgress({ stage: '检查数据库位置', progress: 0 });
    }
    
    // 如果新位置已有数据库，跳过迁移
    if (fs.existsSync(newDbPath)) {
      console.log('数据库已存在于目标位置，跳过迁移:', newDbPath);
      if (options?.onProgress) {
        options.onProgress({ stage: '完成', progress: 100 });
      }
      return;
    }
    
    // 检查旧位置是否有数据库
    if (fs.existsSync(oldDbPath) && oldDbPath !== newDbPath) {
      console.log('发现旧数据库，开始迁移:', oldDbPath, '->', newDbPath);
      
      if (options?.onProgress) {
        options.onProgress({ stage: '复制数据库文件', progress: 50 });
      }
      
      try {
        // 确保目标目录存在
        fs.mkdirSync(path.dirname(newDbPath), { recursive: true });
        
        // 复制数据库文件
        fs.copyFileSync(oldDbPath, newDbPath);
        
        console.log('✅ 数据库迁移完成');
        
        if (options?.onProgress) {
          options.onProgress({ stage: '完成', progress: 100 });
        }
      } catch (error) {
        console.error('❌ 数据库迁移失败:', error);
        
        // 清理可能的部分文件
        if (fs.existsSync(newDbPath)) {
          try {
            fs.unlinkSync(newDbPath);
          } catch (cleanupError) {
            console.error('清理失败的迁移文件时出错:', cleanupError);
          }
        }
        
        throw error;
      }
    } else {
      console.log('未发现需要迁移的旧数据库，将创建新数据库');
      if (options?.onProgress) {
        options.onProgress({ stage: '完成', progress: 100 });
      }
    }
  }

  /**
   * 基于SettingsService创建DatabaseManager实例
   */
  static createFromSettings(settingsService: any, options?: DatabaseMigrationOptions): DatabaseManager {
    const storagePath = settingsService.getStoragePath();
    const dbPath = path.join(storagePath, 'database.db');
    return new DatabaseManager(dbPath, options);
  }
  
  private initDatabase() {
    try {
      // 启用外键约束
      this.db.pragma('foreign_keys = ON');
      
      // 启用WAL模式提升并发性能
      this.db.pragma('journal_mode = WAL');
      
      // 优化缓存大小
      this.db.pragma('cache_size = 10000');
      
      // 设置同步模式为NORMAL
      this.db.pragma('synchronous = NORMAL');
      
      // 创建所有表
      this.createTables();
      
      // 如果数据库为空，插入初始数据
      this.insertInitialData();
      
      console.log('SQLite数据库初始化完成');
    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw error;
    }
  }
  
  private createTables() {
    // 分类表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        thumbnail_path TEXT,
        thumbnail_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );
    `);

    // 图片表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS images (
        id TEXT PRIMARY KEY,
        category_id TEXT NOT NULL,
        title TEXT,
        original_filename TEXT,
        stored_filename TEXT,
        relative_file_path TEXT,
        relative_thumbnail_path TEXT,
        mime_type TEXT,
        size_bytes INTEGER,
        description TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        file_metadata TEXT,
        exif_info TEXT,
        image_url TEXT NOT NULL,
        thumbnail_url TEXT,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
      );
    `);

    // 标签表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS tags (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );
    `);

    // 图片标签关联表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS image_tags (
        image_id TEXT NOT NULL,
        tag_id TEXT NOT NULL,
        PRIMARY KEY (image_id, tag_id),
        FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
      );
    `);

    // 性能优化索引
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_images_category ON images(category_id);
      CREATE INDEX IF NOT EXISTS idx_images_created ON images(created_at);
      CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
      CREATE INDEX IF NOT EXISTS idx_image_tags_image ON image_tags(image_id);
      CREATE INDEX IF NOT EXISTS idx_image_tags_tag ON image_tags(tag_id);
    `);
  }
  
  private insertInitialData() {
    try {
      const count = this.db.prepare('SELECT COUNT(*) FROM categories').pluck().get() as number;
      if (count > 0) return;

      console.log('插入初始数据...');
      
      const insertCategory = this.db.prepare(`
        INSERT OR IGNORE INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);


      // 插入初始分类
      const categories = [
        {
          id: 'magpie',
          name: '喜鹊',
          description: '聪明的黑白相间鸟类，善于模仿声音，常见于城市和乡村',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 'sparrow',
          name: '麻雀',
          description: '小型褐色鸟类，适应性强，广泛分布于人类居住区',
          created_at: '2024-01-01T01:00:00Z',
          updated_at: '2024-01-01T01:00:00Z'
        },
        {
          id: 'turtle-dove',
          name: '斑鸠',
          description: '中型鸟类，羽毛有斑点，性情温和，常见于公园和树林',
          created_at: '2024-01-01T02:00:00Z',
          updated_at: '2024-01-01T02:00:00Z'
        }
      ];

      // 不插入初始标签数据

      // 使用事务插入数据
      const insertInitialData = this.db.transaction(() => {
        for (const category of categories) {
          insertCategory.run(
            category.id,
            category.name,
            category.description,
            null,
            null,
            category.created_at,
            category.updated_at
          );
        }
      });

      insertInitialData();
      console.log('初始数据插入完成');
    } catch (error) {
      console.error('插入初始数据失败:', error);
    }
  }
  
  // 获取SQLite数据库实例
  getDatabase() {
    return this.db;
  }
  
  // 测试数据库连接
  testConnection() {
    try {
      const categoriesResult = this.db.prepare('SELECT COUNT(*) as count FROM categories').get() as { count: number };
      const imagesResult = this.db.prepare('SELECT COUNT(*) as count FROM images').get() as { count: number };
      const tagsResult = this.db.prepare('SELECT COUNT(*) as count FROM tags').get() as { count: number };
      const imageTagsResult = this.db.prepare('SELECT COUNT(*) as count FROM image_tags').get() as { count: number };
      
      const result = {
        success: true,
        categories: categoriesResult.count,
        tags: tagsResult.count,
        images: imagesResult.count,
        imageTags: imageTagsResult.count,
        message: '数据库连接成功',
        mode: 'SQLite'
      };
      
      console.log('数据库连接测试成功:', result);
      return result;
    } catch (error) {
      console.error('数据库连接测试失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: '数据库连接失败'
      };
    }
  }
  
  // 获取数据库统计信息
  getStats() {
    try {
      const stats = {
        categories: { count: this.db.prepare('SELECT COUNT(*) FROM categories').pluck().get() as number },
        images: { count: this.db.prepare('SELECT COUNT(*) FROM images').pluck().get() as number },
        tags: { count: this.db.prepare('SELECT COUNT(*) FROM tags').pluck().get() as number },
        imageTags: { count: this.db.prepare('SELECT COUNT(*) FROM image_tags').pluck().get() as number }
      };
      
      return {
        success: true,
        data: stats,
        message: '获取统计信息成功',
        mode: 'SQLite'
      };
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: '获取统计信息失败'
      };
    }
  }
  
  // 重置数据库
  resetDatabase() {
    try {
      console.log('开始重置数据库...');
      
      // 使用事务确保数据完整性
      const resetTransaction = this.db.transaction(() => {
        // 清空所有表数据（按依赖关系顺序）
        this.db.exec('DELETE FROM image_tags');
        this.db.exec('DELETE FROM images');
        this.db.exec('DELETE FROM tags');
        this.db.exec('DELETE FROM categories');
        
        // 重置自动递增序列（如果存在的话）
        const sequenceTableExists = this.db.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name='sqlite_sequence'
        `).get();
        
        if (sequenceTableExists) {
          this.db.exec('DELETE FROM sqlite_sequence WHERE name IN ("categories", "images", "tags", "image_tags")');
        }
        
        console.log('数据库表已清空');
      });
      
      // 执行清空操作
      resetTransaction();
      
      // 重新插入初始数据
      this.insertInitialData();
      
      console.log('数据库重置完成');
      
      return {
        success: true,
        message: '数据库重置成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('数据库重置失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: '数据库重置失败'
      };
    }
  }

  /**
   * 获取数据库文件路径
   */
  getDatabasePath(): string {
    console.log('🗂️ DatabaseManager.getDatabasePath() 返回:', this.dbPath);
    console.log('📊 数据库文件状态:');
    console.log('  - 文件存在:', fs.existsSync(this.dbPath));
    if (fs.existsSync(this.dbPath)) {
      const stats = fs.statSync(this.dbPath);
      console.log('  - 文件大小:', stats.size, '字节');
      console.log('  - 最后修改:', stats.mtime);
    }
    return this.dbPath;
  }

  /**
   * 创建数据库备份
   */
  createDatabaseBackup(): { success: boolean; backup?: Buffer; message: string } {
    try {
      console.log('📋 开始创建数据库备份...');
      
      // 检查数据库文件是否存在
      if (!fs.existsSync(this.dbPath)) {
        return {
          success: false,
          message: '数据库文件不存在'
        };
      }

      // 执行WAL检查点，确保所有数据写入主数据库文件
      try {
        this.db.pragma('wal_checkpoint(FULL)');
        console.log('✅ WAL检查点完成');
      } catch (error) {
        console.warn('⚠️ WAL检查点失败:', error);
        // 不影响备份操作，继续执行
      }

      // 读取数据库文件
      const backup = fs.readFileSync(this.dbPath);
      
      console.log('✅ 数据库备份创建成功，大小:', backup.length, '字节');
      
      return {
        success: true,
        backup,
        message: '数据库备份创建成功'
      };

    } catch (error) {
      console.error('❌ 创建数据库备份失败:', error);
      return {
        success: false,
        message: `创建备份失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 从备份数据恢复数据库
   */
  restoreFromBackup(backupData: Buffer): { success: boolean; message: string } {
    try {
      console.log('🔄 开始从备份恢复数据库...');
      
      // 验证备份数据
      if (!backupData || backupData.length === 0) {
        return {
          success: false,
          message: '备份数据为空'
        };
      }

      // 验证SQLite文件格式
      if (!this.validateSQLiteBackup(backupData)) {
        return {
          success: false,
          message: '备份文件格式无效，不是有效的SQLite数据库'
        };
      }

      // 安全关闭当前数据库连接
      const closeResult = this.closeDatabaseSafely();
      if (!closeResult.success) {
        return {
          success: false,
          message: `关闭数据库失败: ${closeResult.message}`
        };
      }

      // 备份当前数据库文件（以防恢复失败）
      const backupPath = `${this.dbPath}.backup-${Date.now()}`;
      try {
        if (fs.existsSync(this.dbPath)) {
          fs.copyFileSync(this.dbPath, backupPath);
          console.log('✅ 当前数据库已备份到:', backupPath);
        }
      } catch (error) {
        console.warn('⚠️ 备份当前数据库失败:', error);
        // 继续恢复操作
      }

      try {
        // 写入新的数据库文件
        fs.writeFileSync(this.dbPath, backupData);
        console.log('✅ 数据库文件已恢复');

        // 重新初始化数据库连接
        this.db = new Database(this.dbPath);
        
        // 验证恢复的数据库
        const testResult = this.testConnection();
        if (!testResult.success) {
          throw new Error('恢复的数据库无法正常连接');
        }

        // 删除临时备份文件
        try {
          if (fs.existsSync(backupPath)) {
            fs.unlinkSync(backupPath);
            console.log('✅ 临时备份文件已清理');
          }
        } catch (error) {
          console.warn('⚠️ 清理临时备份文件失败:', error);
        }

        console.log('✅ 数据库恢复完成');
        
        return {
          success: true,
          message: '数据库恢复成功'
        };

      } catch (error) {
        console.error('❌ 恢复数据库文件失败:', error);
        
        // 尝试从备份恢复
        try {
          if (fs.existsSync(backupPath)) {
            fs.copyFileSync(backupPath, this.dbPath);
            this.db = new Database(this.dbPath);
            console.log('✅ 已从备份恢复原数据库');
          }
        } catch (restoreError) {
          console.error('❌ 从备份恢复失败:', restoreError);
        }

        return {
          success: false,
          message: `恢复失败: ${error instanceof Error ? error.message : String(error)}`
        };
      }

    } catch (error) {
      console.error('❌ 数据库恢复失败:', error);
      return {
        success: false,
        message: `恢复失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 安全关闭数据库连接
   */
  closeDatabaseSafely(): { success: boolean; message: string } {
    try {
      // 执行WAL检查点，确保所有数据写入
      try {
        this.db.pragma('wal_checkpoint(FULL)');
      } catch (error) {
        console.warn('⚠️ 关闭前WAL检查点失败:', error);
      }

      // 关闭数据库连接
      this.db.close();
      console.log('✅ 数据库连接已安全关闭');
      
      return {
        success: true,
        message: '数据库连接已关闭'
      };

    } catch (error) {
      console.error('❌ 安全关闭数据库失败:', error);
      return {
        success: false,
        message: `关闭数据库失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 验证SQLite备份文件格式
   */
  private validateSQLiteBackup(buffer: Buffer): boolean {
    try {
      // SQLite文件的前16个字节是固定的文件头
      const sqliteHeader = 'SQLite format 3\0';
      const headerBuffer = Buffer.from(sqliteHeader, 'utf8');
      
      if (buffer.length < 16) {
        console.error('备份文件太小，不是有效的SQLite文件');
        return false;
      }
      
      // 检查文件头是否匹配
      const isValid = buffer.subarray(0, 16).equals(headerBuffer);
      
      if (!isValid) {
        console.error('备份文件头不匹配，不是有效的SQLite文件');
      }
      
      return isValid;
    } catch (error) {
      console.error('验证SQLite备份文件格式失败:', error);
      return false;
    }
  }
  
  // 关闭数据库连接
  close() {
    try {
      this.db.close();
      console.log('数据库连接已关闭');
    } catch (error) {
      console.error('关闭数据库时发生错误:', error);
    }
  }
}