import { app, BrowserWindow, ipcMain, protocol, Menu, shell, dialog } from 'electron';
import path from 'path';
import fs from 'fs';
import Database from 'better-sqlite3';
import { DatabaseManager } from './database';
import { CategoryService } from './services/CategoryService';
import { ImageService } from './services/ImageService';
import { TagService } from './services/TagService';
import { SettingsService } from './services/SettingsService';
import { DatabaseSyncService } from './services/DatabaseSyncService';
import { OSSService } from './services/OSSService';
import { BatchImportService } from './services/BatchImportService';
import { FolderStructureValidator } from './services/FolderStructureValidator';

const isDev = process.env.NODE_ENV === 'development';
let dbManager: DatabaseManager;
let categoryService: CategoryService;
let imageService: ImageService;
let tagService: TagService;
let settingsService: SettingsService;
let ossService: OSSService;
let databaseSyncService: DatabaseSyncService;
let batchImportService: BatchImportService;
let folderValidator: FolderStructureValidator;
let mainWindow: BrowserWindow;

// 首次启动存储位置选择对话框
async function showFirstTimeSetupDialog(): Promise<boolean> {
  try {
    console.log('🚀 首次启动检测，显示存储位置选择对话框');
    
    // 显示欢迎信息
    const welcomeResult = await dialog.showMessageBox(mainWindow || null as any, {
      type: 'info',
      title: 'Pokedex - 欢迎',
      message: '欢迎使用 Pokedex 图片管理应用！',
      detail: '请选择您希望存储图片的位置。您可以选择默认位置或自定义路径。',
      buttons: ['选择自定义位置', '使用默认位置'],
      defaultId: 1,
      cancelId: 1
    });

    let storagePath: string;

    if (welcomeResult.response === 0) {
      // 用户选择自定义位置
      const directoryResult = await dialog.showOpenDialog(mainWindow || null as any, {
        title: '选择图片存储位置',
        message: '请选择一个文件夹用于存储您的图片',
        properties: ['openDirectory', 'createDirectory'],
        buttonLabel: '选择此文件夹'
      });

      if (directoryResult.canceled || !directoryResult.filePaths.length) {
        console.log('❌ 用户取消了目录选择，使用默认位置');
        storagePath = settingsService.getSettings().storagePath;
      } else {
        storagePath = directoryResult.filePaths[0];
        console.log('✅ 用户选择自定义存储位置:', storagePath);
      }
    } else {
      // 使用默认位置
      storagePath = settingsService.getSettings().storagePath;
      console.log('✅ 使用默认存储位置:', storagePath);
    }

    // 保存设置（移除usesCategoryFolders，始终使用分类文件夹结构）
    const saved = settingsService.saveSettings({
      storagePath,
      isFirstTimeSetup: true
    });

    if (saved) {
      // 显示确认信息
      await dialog.showMessageBox(mainWindow || null as any, {
        type: 'info',
        title: '设置完成',
        message: '存储配置已完成！',
        detail: `存储位置: ${storagePath}\n\n图片将按分类自动整理到独立文件夹中。您可以随时通过应用菜单更改存储位置。`,
        buttons: ['开始使用']
      });

      console.log('✅ 首次设置完成');
      return true;
    } else {
      throw new Error('保存设置失败');
    }
  } catch (error) {
    console.error('❌ 首次设置失败:', error);
    
    // 显示错误信息并使用默认设置
    await dialog.showErrorBox(
      '设置错误', 
      `首次设置失败: ${error instanceof Error ? error.message : String(error)}\n\n将使用默认设置继续运行。`
    );
    
    return false;
  }
}

// 更改存储位置功能
async function changeStorageLocation(): Promise<void> {
  try {
    console.log('🔧 开始更改存储位置流程');
    
    // 显示当前存储位置
    const currentSettings = settingsService.getSettings();
    const currentPath = currentSettings.storagePath;
    
    const confirmResult = await dialog.showMessageBox(mainWindow, {
      type: 'question',
      title: '更改存储位置',
      message: '确定要更改图片存储位置吗？',
      detail: `当前存储位置: ${currentPath}\n\n更改后会将现有图片移动到新位置。`,
      buttons: ['选择新位置', '取消'],
      defaultId: 0,
      cancelId: 1
    });

    if (confirmResult.response === 1) {
      console.log('❌ 用户取消了存储位置更改');
      return;
    }

    // 选择新的存储位置
    const directoryResult = await dialog.showOpenDialog(mainWindow, {
      title: '选择新的图片存储位置',
      message: '请选择一个文件夹用于存储您的图片',
      properties: ['openDirectory', 'createDirectory'],
      buttonLabel: '选择此文件夹'
    });

    if (directoryResult.canceled || !directoryResult.filePaths.length) {
      console.log('❌ 用户取消了目录选择');
      return;
    }

    const newStoragePath = directoryResult.filePaths[0];
    
    // 检查是否选择了相同的路径
    if (newStoragePath === currentPath) {
      await dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '相同路径',
        message: '您选择的路径与当前存储位置相同。',
        buttons: ['确定']
      });
      return;
    }

    // 显示迁移确认
    const migrationConfirm = await dialog.showMessageBox(mainWindow, {
      type: 'warning',
      title: '确认迁移',
      message: '开始迁移数据？',
      detail: `将从: ${currentPath}\n迁移到: ${newStoragePath}\n\n此过程可能需要一些时间，建议在迁移期间不要关闭应用。`,
      buttons: ['开始迁移', '取消'],
      defaultId: 0,
      cancelId: 1
    });

    if (migrationConfirm.response === 1) {
      console.log('❌ 用户取消了数据迁移');
      return;
    }

    // 执行实际的数据迁移
    console.log('🚀 开始执行存储位置迁移');
    
    // 先执行文件迁移
    const migrationResult = await imageService.migrateStorageLocation(newStoragePath);
    
    if (migrationResult.success) {
      // 首先更新设置中的存储路径
      const updateSuccess = settingsService.updateStoragePath(newStoragePath);
      
      if (updateSuccess) {
        // 然后执行数据库迁移到新位置
        try {
          console.log('🗄️ 开始迁移数据库到新位置');
          
          // 关闭当前数据库连接
          if (dbManager) {
            dbManager.close();
          }
          
          // 使用新的存储路径重新初始化数据库管理器
          dbManager = DatabaseManager.createFromSettings(settingsService, {
            onProgress: (info) => {
              console.log(`📋 数据库迁移进度: ${info.stage} (${info.progress}%)`);
            }
          });
          
          // 重新初始化依赖于数据库的服务
          categoryService = new CategoryService(dbManager);
          imageService = new ImageService(dbManager, settingsService);
          tagService = new TagService(dbManager);
          
          // 重新初始化批量导入服务
          batchImportService = new BatchImportService(dbManager, settingsService);
          folderValidator = new FolderStructureValidator();

          // 重新设置服务间的依赖关系
          categoryService.setImageService(imageService);
          
          console.log('✅ 数据库迁移完成，新路径:', dbManager.getDatabasePath());
          
        } catch (dbError) {
          console.error('❌ 数据库迁移失败:', dbError);
          
          // 数据库迁移失败，显示警告但不阻止操作
          await dialog.showMessageBox(mainWindow, {
            type: 'warning',
            title: '数据库迁移警告',
            message: '数据库迁移失败，但文件迁移成功',
            detail: `数据库迁移失败: ${dbError instanceof Error ? dbError.message : String(dbError)}\n\n图片文件已成功迁移到新位置，但数据库仍在旧位置。建议重启应用以确保数据一致性。`,
            buttons: ['确定']
          });
        }
        // 显示详细的迁移结果
        const details = migrationResult.details;
        let detailMessage = `新的存储位置: ${newStoragePath}\n\n`;
        
        if (details) {
          detailMessage += `迁移统计:\n`;
          detailMessage += `• 总计: ${details.total} 张图片\n`;
          detailMessage += `• 成功: ${details.success} 张\n`;
          detailMessage += `• 失败: ${details.failed} 张\n`;
          
          if (details.migratedCategories.length > 0) {
            detailMessage += `\n按分类统计:\n`;
            details.migratedCategories.forEach((cat: any) => {
              detailMessage += `• ${cat.category}: ${cat.files} 张\n`;
            });
          }
          
          if (details.failedFiles.length > 0) {
            detailMessage += `\n失败文件:\n`;
            details.failedFiles.slice(0, 5).forEach((file: string) => {
              detailMessage += `• ${file}\n`;
            });
            if (details.failedFiles.length > 5) {
              detailMessage += `• ...(还有 ${details.failedFiles.length - 5} 个)\n`;
            }
          }
        }
        
        await dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: '迁移完成',
          message: migrationResult.message,
          detail: detailMessage,
          buttons: ['确定']
        });
        
        console.log('✅ 存储位置更改成功:', newStoragePath);
      } else {
        throw new Error('更新存储设置失败');
      }
    } else {
      // 迁移失败，显示错误信息
      await dialog.showErrorBox(
        '迁移失败',
        `文件迁移失败: ${migrationResult.message}\n\n存储设置未更改，请检查文件权限或磁盘空间。`
      );
      throw new Error(`文件迁移失败: ${migrationResult.message}`);
    }
    
  } catch (error) {
    console.error('❌ 更改存储位置失败:', error);
    
    await dialog.showErrorBox(
      '更改失败', 
      `更改存储位置失败: ${error instanceof Error ? error.message : String(error)}\n\n请检查目录权限或选择其他位置。`
    );
  }
}

// 显示OSS配置对话框
async function showOSSConfigDialog(): Promise<void> {
  try {
    // 直接打开OSS配置窗口
    await createOSSConfigWindow();
  } catch (error) {
    console.error('❌ 显示OSS配置对话框失败:', error);
    await dialog.showErrorBox('配置错误', `显示OSS配置对话框失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 创建OSS配置窗口
async function createOSSConfigWindow(): Promise<void> {
  // 如果窗口已经存在，则聚焦到该窗口
  const existingWindow = BrowserWindow.getAllWindows().find(win => win.getTitle() === '存储设置');
  if (existingWindow) {
    existingWindow.focus();
    return;
  }

  const configWindow = new BrowserWindow({
    width: 600,
    height: 700,
    resizable: false,
    modal: true,
    parent: mainWindow,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    title: '存储设置',
    center: true,
    autoHideMenuBar: true
  });

  // 加载配置页面
  const htmlPath = app.isPackaged 
    ? path.join(__dirname, 'oss-config-window.html')
    : path.join(__dirname, '../../electron/oss-config-window.html');
  
  await configWindow.loadFile(htmlPath);

  // 窗口关闭时清理
  configWindow.on('closed', () => {
    console.log('OSS配置窗口已关闭');
  });
}


// 测试OSS连接
async function testOSSConnection(): Promise<void> {
  try {
    const config = settingsService.getOSSConfig();
    if (!config) {
      await dialog.showErrorBox('配置错误', '未找到OSS配置');
      return;
    }
    
    const { OSSService } = await import('./services/OSSService');
    const ossService = new OSSService(config);
    const testResult = await ossService.testConnection();
    
    if (testResult.success) {
      await dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '连接成功',
        message: 'OSS连接测试成功!',
        detail: testResult.message
      });
    } else {
      await dialog.showErrorBox('连接失败', `OSS连接测试失败: ${testResult.message}`);
    }
  } catch (error) {
    console.error('❌ OSS连接测试失败:', error);
    await dialog.showErrorBox('测试失败', `OSS连接测试失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 显示存储类型切换对话框
async function showStorageTypeDialog(): Promise<void> {
  try {
    const currentType = settingsService.getStorageType();
    const isOSSConfigured = settingsService.isOSSConfigured();
    
    const typeOptions = [
      { type: 'local', label: '本地存储', description: '图片存储在本地文件系统' },
      { type: 'oss', label: 'OSS存储', description: '图片存储在对象存储服务' }
    ];
    
    const statusMessage = `当前存储类型: ${currentType === 'local' ? '本地存储' : 'OSS存储'}\n` +
      `OSS配置状态: ${isOSSConfigured ? '已配置' : '未配置'}`;
    
    const buttons = ['切换到本地存储', '切换到OSS存储', '取消'];
    
    const switchResult = await dialog.showMessageBox(mainWindow, {
      type: 'question',
      title: '切换存储类型',
      message: '选择存储类型',
      detail: statusMessage,
      buttons,
      defaultId: currentType === 'local' ? 1 : 0,
      cancelId: 2
    });
    
    if (switchResult.response === 2) {
      return; // 取消
    }
    
    const targetType = switchResult.response === 0 ? 'local' : 'oss';
    
    if (targetType === currentType) {
      await dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '无需切换',
        message: '当前已经是所选的存储类型'
      });
      return;
    }
    
    // 检查OSS配置
    if (targetType === 'oss' && !isOSSConfigured) {
      const configResult = await dialog.showMessageBox(mainWindow, {
        type: 'warning',
        title: '配置不完整',
        message: '切换到OSS存储需要先配置OSS',
        detail: '请先配置OSS存储后再切换',
        buttons: ['立即配置', '取消'],
        defaultId: 0,
        cancelId: 1
      });
      
      if (configResult.response === 0) {
        await showOSSConfigDialog();
      }
      return;
    }
    
    // 确认切换
    const confirmResult = await dialog.showMessageBox(mainWindow, {
      type: 'warning',
      title: '确认切换',
      message: `确定要切换到${targetType === 'local' ? '本地存储' : 'OSS存储'}吗?`,
      detail: '切换存储类型后，新上传的图片将使用新的存储方式',
      buttons: ['确定切换', '取消'],
      defaultId: 0,
      cancelId: 1
    });
    
    if (confirmResult.response === 1) {
      return; // 取消
    }
    
    // 执行切换
    const switchSuccess = settingsService.setStorageType(targetType);
    if (switchSuccess) {
      await dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '切换成功',
        message: `已成功切换到${targetType === 'local' ? '本地存储' : 'OSS存储'}`,
        detail: '新上传的图片将使用新的存储方式'
      });
    } else {
      await dialog.showErrorBox('切换失败', '切换存储类型失败');
    }
  } catch (error) {
    console.error('❌ 显示存储类型对话框失败:', error);
    await dialog.showErrorBox('切换失败', `显示存储类型对话框失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 创建应用菜单
function createMenu() {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: '文件',
      submenu: [
        {
          label: '新建分类',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-action', 'new-category');
          }
        },
        {
          label: '导入图片',
          accelerator: 'CmdOrCtrl+I',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile', 'multiSelections'],
              filters: [
                { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] }
              ]
            });
            
            if (!result.canceled && result.filePaths.length > 0) {
              mainWindow.webContents.send('menu-action', 'import-images', result.filePaths);
            }
          }
        },
        { type: 'separator' },
        {
          label: '导出数据',
          accelerator: 'CmdOrCtrl+E',
          click: async () => {
            const result = await dialog.showSaveDialog(mainWindow, {
              defaultPath: 'pokedex-data.json',
              filters: [
                { name: 'JSON文件', extensions: ['json'] }
              ]
            });
            
            if (!result.canceled && result.filePath) {
              try {
                const db = dbManager.getDatabase() as Database.Database;
                const categories = db.prepare('SELECT * FROM categories').all();
                const tags = db.prepare('SELECT * FROM tags').all();
                const images = db.prepare('SELECT * FROM images').all();
                const imageTags = db.prepare('SELECT * FROM image_tags').all();
                
                const dbData = {
                  categories,
                  tags,
                  images,
                  imageTags
                };
                
                fs.writeFileSync(result.filePath, JSON.stringify(dbData, null, 2));
                dialog.showMessageBox(mainWindow, {
                  type: 'info',
                  title: '导出成功',
                  message: '数据已成功导出到指定位置。'
                });
              } catch (error) {
                dialog.showErrorBox('导出失败', `导出数据时发生错误：${error instanceof Error ? error.message : String(error)}`);
              }
            }
          }
        },
        { type: 'separator' },
        {
          label: '更改存储位置',
          click: async () => {
            await changeStorageLocation();
          }
        },
        {
          label: '打开存储文件夹',
          click: () => {
            const storagePath = settingsService.getStoragePath();
            shell.openPath(storagePath);
          }
        },
        { type: 'separator' },
        {
          label: '存储设置',
          click: async () => {
            await showOSSConfigDialog();
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' },
        { label: '全选', accelerator: 'CmdOrCtrl+A', role: 'selectAll' },
        { type: 'separator' },
        {
          label: '搜索',
          accelerator: 'CmdOrCtrl+F',
          click: () => {
            mainWindow.webContents.send('menu-action', 'search');
          }
        }
      ]
    },
    {
      label: '查看',
      submenu: [
        {
          label: '刷新',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.webContents.reload();
          }
        },
        {
          label: '强制刷新',
          accelerator: 'CmdOrCtrl+Shift+R',
          click: () => {
            mainWindow.webContents.reloadIgnoringCache();
          }
        },
        { type: 'separator' },
        {
          label: '实际大小',
          accelerator: 'CmdOrCtrl+0',
          click: () => {
            mainWindow.webContents.setZoomLevel(0);
          }
        },
        {
          label: '放大',
          accelerator: 'CmdOrCtrl+Plus',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel();
            mainWindow.webContents.setZoomLevel(currentZoom + 0.5);
          }
        },
        {
          label: '缩小',
          accelerator: 'CmdOrCtrl+-',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel();
            mainWindow.webContents.setZoomLevel(currentZoom - 0.5);
          }
        },
        { type: 'separator' },
        {
          label: '全屏',
          accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
          click: () => {
            mainWindow.setFullScreen(!mainWindow.isFullScreen());
          }
        }
      ]
    },
    {
      label: '窗口',
      submenu: [
        {
          label: '最小化',
          accelerator: 'CmdOrCtrl+M',
          role: 'minimize'
        },
        {
          label: '关闭',
          accelerator: 'CmdOrCtrl+W',
          role: 'close'
        }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '快捷键',
          click: () => {
            mainWindow.webContents.send('menu-action', 'show-shortcuts');
          }
        },
        {
          label: '关于数据库',
          click: async () => {
            try {
              const stats = dbManager.getStats();
              if (stats.success) {
                const message = `数据库状态：正常\n` +
                  `分类数量：${stats.data?.categories.count || 0}\n` +
                  `图片数量：${stats.data?.images.count || 0}\n` +
                  `标签数量：${stats.data?.tags.count || 0}\n` +
                  `标签关联：${stats.data?.imageTags.count || 0}`;
                
                dialog.showMessageBox(mainWindow, {
                  type: 'info',
                  title: '数据库信息',
                  message
                });
              }
            } catch (error) {
              dialog.showErrorBox('数据库错误', `无法获取数据库信息：${error instanceof Error ? error.message : String(error)}`);
            }
          }
        },
        { type: 'separator' },
        {
          label: '打开数据目录',
          click: () => {
            const userDataPath = app.getPath('userData');
            shell.openPath(userDataPath);
          }
        },
        {
          label: '开发者工具',
          accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
          click: () => {
            mainWindow.webContents.toggleDevTools();
          }
        },
        { type: 'separator' },
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于',
              message: 'Pokedex',
              detail: `版本：1.0.0\n构建于 Electron 和 React\n\n现代化图片管理和识别系统\n支持标签管理和高级搜索功能`
            });
          }
        }
      ]
    }
  ];

  // macOS需要特殊处理菜单
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { label: '关于 ' + app.getName(), role: 'about' },
        { type: 'separator' },
        { label: '服务', role: 'services' },
        { type: 'separator' },
        { label: '隐藏 ' + app.getName(), accelerator: 'Command+H', role: 'hide' },
        { label: '隐藏其他', accelerator: 'Command+Shift+H', role: 'hideOthers' },
        { label: '显示全部', role: 'unhide' },
        { type: 'separator' },
        { label: '退出', accelerator: 'Command+Q', click: () => app.quit() }
      ]
    });

    // 调整窗口菜单为macOS风格
    (template[4].submenu as Electron.MenuItemConstructorOptions[]).push(
      { type: 'separator' },
      { label: '前置全部窗口', role: 'front' }
    );
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 设置日志文件 - 使用用户数据目录确保可写性
function initializeLogging() {
  const userDataPath = app.getPath('userData');
  const logFile = path.join(userDataPath, 'electron.log');
  
  // 确保日志目录存在
  const logDir = path.dirname(logFile);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;

  console.log = (...args) => {
    try {
      const timestamp = new Date().toISOString();
      const message = `[${timestamp}] LOG: ${args.join(' ')}\n`;
      fs.appendFileSync(logFile, message);
      originalConsoleLog(...args);
    } catch (error) {
      // 如果日志写入失败，只输出到控制台，不要崩溃
      originalConsoleLog('日志写入失败:', error);
      originalConsoleLog(...args);
    }
  };

  console.error = (...args) => {
    try {
      const timestamp = new Date().toISOString();
      const message = `[${timestamp}] ERROR: ${args.join(' ')}\n`;
      fs.appendFileSync(logFile, message);
      originalConsoleError(...args);
    } catch (error) {
      // 如果日志写入失败，只输出到控制台，不要崩溃
      originalConsoleError('日志写入失败:', error);
      originalConsoleError(...args);
    }
  };
  
  console.log('日志系统初始化完成，日志文件:', logFile);
}

// 初始化服务
function initializeServices() {
  try {
    console.log('🔄 开始初始化服务...');
    
    // 先初始化设置服务
    settingsService = new SettingsService();
    console.log('设置服务初始化成功');

    // 关闭旧的数据库连接（如果存在）
    if (dbManager) {
      console.log('🔒 关闭旧的数据库连接...');
      dbManager.close();
    }

    // 使用createFromSettings方法初始化数据库管理器，支持数据库迁移
    dbManager = DatabaseManager.createFromSettings(settingsService, {
      onProgress: (info) => {
        console.log(`📋 数据库迁移进度: ${info.stage} (${info.progress}%)`);
      }
    });
    console.log('数据库管理器初始化完成，路径:', dbManager.getDatabasePath());
    
    categoryService = new CategoryService(dbManager);
    imageService = new ImageService(dbManager, settingsService);
    tagService = new TagService(dbManager);

    // 初始化OSS和数据库同步服务
    ossService = new OSSService();
    databaseSyncService = new DatabaseSyncService(ossService, settingsService, dbManager);

    // 初始化批量导入服务
    batchImportService = new BatchImportService(dbManager, settingsService);
    folderValidator = new FolderStructureValidator();

    // 设置服务间的依赖关系
    categoryService.setImageService(imageService);

    console.log('数据库管理器和服务初始化成功');
  } catch (error) {
    console.error('服务初始化失败:', error);
    throw error;
  }
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, '../preload/preload.js')
    },
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    show: false // 延迟显示，等待内容加载
  });

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // 发送初始化数据到渲染进程
    setTimeout(() => {
      mainWindow.webContents.send('app-ready');
    }, 1000);
  });

  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
    
    // 捕获渲染进程的控制台输出
    mainWindow.webContents.on('console-message', (_, level, message, line, sourceId) => {
      const logLevel = ['verbose', 'info', 'warning', 'error'][level] || 'log';
      console.log(`[RENDERER-${logLevel.toUpperCase()}] ${message}`);
      if (sourceId) {
        console.log(`  源文件: ${sourceId}:${line}`);
      }
    });
  } else {
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  }

  // 创建菜单
  createMenu();
  
  // 检查是否首次启动，如果是则显示设置对话框
  if (settingsService.isFirstTimeSetup()) {
    console.log('🔧 检测到首次启动，将显示设置对话框');
    
    // 延迟显示对话框，等待窗口完全加载
    setTimeout(async () => {
      await showFirstTimeSetupDialog();
      // 首次设置完成后，确保配置目录存在
      settingsService.ensureConfigDirectory();
    }, 1500);
  } else {
    console.log('✅ 存储设置已存在，当前路径:', settingsService.getStoragePath());
    settingsService.ensureConfigDirectory();
  }
  
  try {
    
    // 注册自定义协议处理器 - 支持本地和OSS存储动态解析
    protocol.registerBufferProtocol('electron', async (request, callback) => {
      try {
        const url = request.url.substr(11); // 移除 'electron://' 前缀
        const [type, filename] = url.split('/');
        
        if (type !== 'file' && type !== 'thumbnail') {
          callback({ error: -6 }); // 文件未找到错误
          return;
        }
        
        const storageType = settingsService.getStorageType();
        
        if (storageType === 'oss') {
          // OSS存储模式：生成签名URL或直接获取文件数据
          let ossPath: string;
          
          if (type === 'file') {
            ossPath = imageService.getImagePath(filename);
          } else {
            ossPath = imageService.getThumbnailPath(filename);
          }
          
          // 检查OSS配置
          const ossConfig = settingsService.getOSSConfig();
          if (!ossConfig || !settingsService.isOSSConfigured()) {
            console.error('❌ OSS未配置，无法获取文件');
            callback({ error: -6 });
            return;
          }
          
          // 创建OSS服务实例并获取文件
          const { OSSService } = await import('./services/OSSService');
          const ossService = new OSSService(ossConfig);
          
          const downloadResult = await ossService.downloadFile(ossPath);
          
          if (downloadResult.success && downloadResult.data) {
            // 确定MIME类型
            const ext = path.extname(filename).toLowerCase();
            const mimeTypes: { [key: string]: string } = {
              '.jpg': 'image/jpeg',
              '.jpeg': 'image/jpeg',
              '.png': 'image/png',
              '.gif': 'image/gif',
              '.webp': 'image/webp',
              '.bmp': 'image/bmp'
            };
            
            const mimeType = mimeTypes[ext] || 'application/octet-stream';
            
            console.log(`✅ 从OSS获取文件成功: ${ossPath}`);
            callback({ 
              data: downloadResult.data,
              mimeType: mimeType
            });
          } else {
            console.error(`❌ 从OSS获取文件失败: ${downloadResult.message}`);
            callback({ error: -6 });
          }
        } else {
          // 本地存储模式（原有逻辑）
          let filePath: string;
          
          if (type === 'file') {
            filePath = imageService.getImagePath(filename);
          } else {
            filePath = imageService.getThumbnailPath(filename);
          }
          
          // 检查文件是否存在
          if (!fs.existsSync(filePath)) {
            console.error(`文件不存在: ${filePath}`);
            callback({ error: -6 }); // 文件未找到错误
            return;
          }
          
          // 读取文件并返回
          try {
            const fileBuffer = fs.readFileSync(filePath);
            
            // 确定MIME类型
            const ext = path.extname(filename).toLowerCase();
            const mimeTypes: { [key: string]: string } = {
              '.jpg': 'image/jpeg',
              '.jpeg': 'image/jpeg',
              '.png': 'image/png',
              '.gif': 'image/gif',
              '.webp': 'image/webp',
              '.bmp': 'image/bmp'
            };
            
            const mimeType = mimeTypes[ext] || 'application/octet-stream';
            
            console.log(`✅ 从本地获取文件成功: ${filePath}`);
            callback({ 
              data: fileBuffer,
              mimeType: mimeType
            });
          } catch (error) {
            console.error(`❌ 读取本地文件失败: ${filePath}`, error);
            callback({ error: -6 });
          }
        }
      } catch (error) {
        console.error('❌ 协议处理器异常:', error);
        callback({ error: -6 });
      }
    });
  } catch (error) {
    console.error('数据库管理器初始化失败:', error);
  }
  
  // IPC处理器已在app.whenReady()中注册
}

// 注册IPC处理器
function registerIpcHandlers() {
  // 数据库相关IPC处理器
  ipcMain.handle('test-database', () => {
    return dbManager.testConnection();
  });
  
  ipcMain.handle('get-database-stats', () => {
    return dbManager.getStats();
  });
  
  ipcMain.handle('reset-database', async () => {
    try {
      console.log('IPC: 收到重置数据库请求');
      const result = dbManager.resetDatabase();
      console.log('IPC: 数据库重置结果:', result.message);
      return result;
    } catch (error) {
      console.error('重置数据库失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: '重置数据库失败'
      };
    }
  });
  
  // 分类相关IPC处理器
  ipcMain.handle('get-categories', async (_, skip = 0, limit = 100) => {
    try {
      console.log('📥 IPC: 收到获取分类请求', { skip, limit });
      const result = await categoryService.getCategories(skip, limit);
      console.log(`📤 IPC: 返回 ${result.categories.length} 条分类记录`);
      console.log(`📋 IPC返回的分类数据:`, result.categories.map((cat: any) => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        created_at: cat.created_at
      })));
      return result.categories;
    } catch (error) {
      console.error('❌ 获取分类失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('create-category', async (_, categoryData) => {
    try {
      return await categoryService.createCategory(categoryData);
    } catch (error) {
      console.error('创建分类失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('update-category', async (_, categoryId, categoryData) => {
    try {
      return await categoryService.updateCategory(categoryId, categoryData);
    } catch (error) {
      console.error('更新分类失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('delete-category', async (_, categoryId) => {
    try {
      console.log('IPC: 收到删除分类请求', { categoryId });
      const result = await categoryService.deleteCategory(categoryId);
      console.log('IPC: 分类删除结果', {
        success: result.success,
        message: result.message,
        imagesDeleted: result.details?.imagesDeleted?.successCount || 0,
        imagesFailed: result.details?.imagesDeleted?.failedCount || 0
      });
      return result;
    } catch (error) {
      console.error('删除分类失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('get-category-by-id', async (_, categoryId) => {
    try {
      return await categoryService.getCategoryById(categoryId);
    } catch (error) {
      console.error('获取分类详情失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('get-category-with-images', async (_, categoryId) => {
    try {
      console.log('IPC: 收到获取分类详情(含图片)请求', { categoryId });
      const result = await categoryService.getCategoryWithImages(categoryId);
      console.log(`IPC: 返回分类详情，包含 ${result?.images?.length || 0} 张图片`);
      return result;
    } catch (error) {
      console.error('获取分类详情(含图片)失败:', error);
      throw error;
    }
  });
  
  // 图片相关IPC处理器
  ipcMain.handle('upload-image', async (_, categoryId, fileBuffer, originalFilename, mimeType, setAsCategoryThumbnail = false) => {
    try {
      console.log('IPC: 收到图片上传请求', { categoryId, originalFilename, size: fileBuffer.length, setAsCategoryThumbnail });
      return await imageService.uploadImage(categoryId, fileBuffer, originalFilename, mimeType, setAsCategoryThumbnail);
    } catch (error) {
      console.error('图片上传失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('get-image-by-id', async (_, imageId) => {
    try {
      return await imageService.getImageById(imageId);
    } catch (error) {
      console.error('获取图片详情失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('update-image', async (_, imageId, imageData) => {
    try {
      return await imageService.updateImage(imageId, imageData);
    } catch (error) {
      console.error('更新图片失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('delete-image', async (_, imageId) => {
    try {
      console.log('IPC: 收到删除图片请求', { imageId });
      const result = await imageService.deleteImage(imageId);
      console.log('IPC: 图片删除结果', result);
      return result;
    } catch (error) {
      console.error('删除图片失败:', error);
      throw error;
    }
  });

  ipcMain.handle('delete-images', async (_, imageIds) => {
    try {
      console.log('IPC: 收到批量删除图片请求', { count: imageIds.length });
      const result = await imageService.deleteImages(imageIds);
      console.log('IPC: 批量删除结果', {
        total: result.totalCount,
        success: result.successCount,
        failed: result.failedCount
      });
      return result;
    } catch (error) {
      console.error('批量删除图片失败:', error);
      throw error;
    }
  });

  ipcMain.handle('validate-delete-conditions', async (_, imageId) => {
    try {
      console.log('IPC: 收到删除条件验证请求', { imageId });
      const result = await imageService.validateDeleteConditions(imageId);
      console.log('IPC: 删除条件验证结果', {
        valid: result.valid,
        errors: result.errors.length,
        warnings: result.warnings.length
      });
      return result;
    } catch (error) {
      console.error('删除条件验证失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('get-image-path', (_, filename) => {
    return imageService.getImagePath(filename);
  });
  
  ipcMain.handle('get-thumbnail-path', (_, filename) => {
    return imageService.getThumbnailPath(filename);
  });
  
  // 标签相关IPC处理器
  ipcMain.handle('get-all-tags', async () => {
    try {
      console.log('IPC: 收到获取所有标签请求');
      const result = await tagService.getAllTags();
      console.log(`IPC: 返回 ${result.length} 条标签记录`);
      return result;
    } catch (error) {
      console.error('获取标签失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('create-tag', async (_, tagData) => {
    try {
      console.log('IPC: 收到创建标签请求', tagData);
      return await tagService.createTag(tagData);
    } catch (error) {
      console.error('创建标签失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('update-tag', async (_, tagId, tagData) => {
    try {
      console.log('IPC: 收到更新标签请求', { tagId, tagData });
      return await tagService.updateTag(tagId, tagData);
    } catch (error) {
      console.error('更新标签失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('delete-tag', async (_, tagId) => {
    try {
      console.log('IPC: 收到删除标签请求', { tagId });
      await tagService.deleteTag(tagId);
    } catch (error) {
      console.error('删除标签失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('get-tag-by-id', async (_, tagId) => {
    try {
      return await tagService.getTagById(tagId);
    } catch (error) {
      console.error('获取标签详情失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('search-tags', async (_, query) => {
    try {
      console.log('IPC: 收到搜索标签请求', { query });
      const result = await tagService.searchTags(query);
      console.log(`IPC: 搜索标签 "${query}" 返回 ${result.length} 条记录`);
      return result;
    } catch (error) {
      console.error('搜索标签失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('add-tag-to-image', async (_, imageId, tagId) => {
    try {
      console.log('IPC: 收到为图片添加标签请求', { imageId, tagId });
      await tagService.addTagToImage(imageId, tagId);
    } catch (error) {
      console.error('为图片添加标签失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('remove-tag-from-image', async (_, imageId, tagId) => {
    try {
      console.log('IPC: 收到从图片移除标签请求', { imageId, tagId });
      await tagService.removeTagFromImage(imageId, tagId);
    } catch (error) {
      console.error('从图片移除标签失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('get-tags-for-image', async (_, imageId) => {
    try {
      return await tagService.getTagsForImage(imageId);
    } catch (error) {
      console.error('获取图片标签失败:', error);
      throw error;
    }
  });
  
  ipcMain.handle('search-images-by-tags', async (_, tagNames) => {
    try {
      console.log('IPC: 收到根据标签搜索图片请求', { tagNames });
      const result = await tagService.searchImagesByTags(tagNames);
      console.log(`IPC: 根据标签搜索图片返回 ${result.length} 张图片`);
      return result;
    } catch (error) {
      console.error('根据标签搜索图片失败:', error);
      throw error;
    }
  });

  // 删除确认对话框相关IPC处理器
  ipcMain.handle('show-delete-confirmation', async (_, options) => {
    try {
      console.log('IPC: 收到删除确认对话框请求', options);
      const { dialog } = require('electron');

      const { type = 'single', count = 1, itemName = '图片' } = options;

      let message: string;
      let detail: string;

      if (type === 'single') {
        message = `确定要删除这张${itemName}吗？`;
        detail = '此操作将永久删除图片文件和相关数据，无法撤销。';
      } else {
        message = `确定要删除这 ${count} 张${itemName}吗？`;
        detail = `此操作将永久删除 ${count} 张图片文件和相关数据，无法撤销。`;
      }

      const result = await dialog.showMessageBox(mainWindow, {
        type: 'warning',
        buttons: ['取消', '删除'],
        defaultId: 0,
        cancelId: 0,
        message,
        detail,
        icon: undefined // 可以添加自定义图标
      });

      const confirmed = result.response === 1;
      console.log('IPC: 删除确认结果', { confirmed, type, count });

      return { confirmed };
    } catch (error) {
      console.error('显示删除确认对话框失败:', error);
      throw error;
    }
  });

  ipcMain.handle('show-delete-progress', async (_, options) => {
    try {
      console.log('IPC: 收到删除进度通知请求', options);
      const { current, total, itemName = '图片' } = options;

      // 这里可以实现进度通知，比如发送到渲染进程
      // 或者显示系统通知
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('delete-progress', {
          current,
          total,
          itemName,
          percentage: Math.round((current / total) * 100)
        });
      }

      console.log(`IPC: 删除进度 ${current}/${total} (${Math.round((current / total) * 100)}%)`);
      return { success: true };
    } catch (error) {
      console.error('发送删除进度失败:', error);
      throw error;
    }
  });

  // 存储配置相关IPC处理器
  ipcMain.handle('get-storage-settings', () => {
    try {
      console.log('IPC: 收到获取存储设置请求');
      const settings = settingsService.getSettings();
      console.log('IPC: 返回存储设置:', settings.storagePath);
      return settings;
    } catch (error) {
      console.error('获取存储设置失败:', error);
      throw error;
    }
  });

  ipcMain.handle('update-storage-settings', async (_, newSettings) => {
    try {
      console.log('IPC: 收到更新存储设置请求', newSettings);
      const success = settingsService.saveSettings(newSettings);
      if (success) {
        // 确保新的配置目录存在
        settingsService.ensureConfigDirectory();
        console.log('IPC: 存储设置更新成功');
      }
      return success;
    } catch (error) {
      console.error('更新存储设置失败:', error);
      throw error;
    }
  });

  ipcMain.handle('select-directory', async () => {
    try {
      console.log('IPC: 收到选择目录请求');
      const result = await dialog.showOpenDialog(mainWindow, {
        title: '选择图片存储位置',
        message: '请选择一个文件夹用于存储您的图片',
        properties: ['openDirectory', 'createDirectory'],
        buttonLabel: '选择此文件夹'
      });

      if (result.canceled || !result.filePaths.length) {
        console.log('IPC: 用户取消了目录选择');
        return null;
      }

      const selectedPath = result.filePaths[0];
      console.log('IPC: 用户选择的目录:', selectedPath);
      return selectedPath;
    } catch (error) {
      console.error('选择目录失败:', error);
      throw error;
    }
  });

  ipcMain.handle('migrate-storage-location', async (_, newStoragePath) => {
    try {
      console.log('IPC: 收到存储迁移请求，目标路径:', newStoragePath);
      
      // 调用ImageService的真实迁移功能
      const result = await imageService.migrateStorageLocation(newStoragePath);
      
      console.log('IPC: 存储迁移完成，结果:', result.message);
      return result;
    } catch (error) {
      console.error('存储迁移失败:', error);
      return {
        success: false,
        message: `迁移失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // ====== OSS配置相关IPC处理器 ======
  
  // 获取OSS配置
  ipcMain.handle('get-oss-config', () => {
    try {
      const config = settingsService.getOSSConfig();
      const storageType = settingsService.getStorageType();
      const isConfigured = settingsService.isOSSConfigured();
      
      return {
        success: true,
        config,
        storageType,
        isConfigured
      };
    } catch (error) {
      console.error('❌ 获取OSS配置失败:', error);
      return {
        success: false,
        message: `获取配置失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 更新OSS配置
  ipcMain.handle('update-oss-config', async (_, config) => {
    try {
      console.log('🔧 更新OSS配置');
      
      // 验证配置
      if (!settingsService.validateOSSConfig(config)) {
        throw new Error('OSS配置不完整');
      }
      
      // 保存配置
      const saveResult = settingsService.setOSSConfig(config);
      if (!saveResult) {
        throw new Error('保存OSS配置失败');
      }
      
      // 重新初始化ImageService的OSS服务
      imageService.reinitializeOSSService();
      
      console.log('✅ OSS配置更新成功');
      return {
        success: true,
        message: '配置更新成功'
      };
    } catch (error) {
      console.error('❌ 更新OSS配置失败:', error);
      return {
        success: false,
        message: `配置更新失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 测试OSS连接
  ipcMain.handle('test-oss-connection', async (_, config) => {
    try {
      console.log('🔗 测试OSS连接');
      
      // 创建临时OSS服务实例进行测试
      const { OSSService } = await import('./services/OSSService');
      const testOSSService = new OSSService(config);
      
      const testResult = await testOSSService.testConnection();
      
      // 销毁临时实例
      testOSSService.destroy();
      
      return testResult;
    } catch (error) {
      console.error('❌ 测试OSS连接失败:', error);
      return {
        success: false,
        message: `连接测试失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 切换存储类型
  ipcMain.handle('switch-storage-type', async (_, storageType) => {
    try {
      console.log('🔄 切换存储类型:', storageType);
      
      // 如果切换到OSS，检查是否已配置
      if (storageType === 'oss' && !settingsService.isOSSConfigured()) {
        throw new Error('OSS未配置，请先配置OSS');
      }
      
      // 更新存储类型
      const updateResult = settingsService.setStorageType(storageType);
      if (!updateResult) {
        throw new Error('更新存储类型失败');
      }
      
      // 重新初始化ImageService
      imageService.reinitializeOSSService();
      
      console.log('✅ 存储类型切换成功:', storageType);
      return {
        success: true,
        message: `已切换到${storageType === 'oss' ? 'OSS' : '本地'}存储`
      };
    } catch (error) {
      console.error('❌ 切换存储类型失败:', error);
      return {
        success: false,
        message: `切换失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 刷新主窗口
  ipcMain.handle('refresh-main-window', async () => {
    try {
      if (mainWindow && !mainWindow.isDestroyed()) {
        console.log('🔄 刷新主窗口');
        mainWindow.webContents.reload();
        return {
          success: true,
          message: '主窗口已刷新'
        };
      } else {
        return {
          success: false,
          message: '主窗口不存在或已销毁'
        };
      }
    } catch (error) {
      console.error('❌ 刷新主窗口失败:', error);
      return {
        success: false,
        message: `刷新失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // ============ 数据库同步相关IPC处理器 ============

  // 检查当前数据库状态
  ipcMain.handle('check-current-database-status', async () => {
    try {
      console.log('🔍 检查当前数据库状态...');
      
      const dbPath = dbManager.getDatabasePath();
      console.log('📂 数据库文件路径:', dbPath);
      
      if (!fs.existsSync(dbPath)) {
        return {
          success: false,
          message: '数据库文件不存在',
          path: dbPath
        };
      }

      const stats = fs.statSync(dbPath);
      console.log('📊 数据库文件统计:', {
        size: stats.size,
        modified: stats.mtime,
        path: dbPath
      });

      // 获取当前分类数据
      const categories = await categoryService.getCategories(0, 50);
      console.log('📋 当前数据库包含的分类:', categories.map(c => ({ id: c.id, name: c.name })));

      return {
        success: true,
        path: dbPath,
        size: stats.size,
        modified: stats.mtime.toISOString(),
        categories: categories.map(c => ({ id: c.id, name: c.name }))
      };
    } catch (error) {
      console.error('❌ 检查数据库状态失败:', error);
      return {
        success: false,
        message: `检查失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 备份数据库到OSS
  ipcMain.handle('backup-database-to-oss', async (event, customName?: string) => {
    try {
      console.log('📤 开始备份数据库到OSS:', customName || '自动命名');
      
      // 确保OSS服务已配置
      const ossConfig = settingsService.getOSSConfig();
      if (!ossConfig) {
        return {
          success: false,
          message: '未配置OSS存储，无法执行备份操作'
        };
      }

      // 更新OSS服务配置
      ossService.updateConfig(ossConfig);

      // 定义进度回调函数
      const progressCallback = (progress: number, message: string) => {
        event.sender.send('backup-progress', { progress, message });
      };

      const result = await databaseSyncService.backupDatabaseToOSS(customName, progressCallback);
      
      if (result.success) {
        console.log('✅ 数据库备份成功:', result.backupInfo?.name);
      } else {
        console.error('❌ 数据库备份失败:', result.message);
      }

      return result;
    } catch (error) {
      console.error('❌ 备份数据库到OSS失败:', error);
      return {
        success: false,
        message: `备份失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 列出OSS上的数据库备份
  ipcMain.handle('list-database-backups', async () => {
    try {
      console.log('📋 获取数据库备份列表');
      
      // 确保OSS服务已配置
      const ossConfig = settingsService.getOSSConfig();
      if (!ossConfig) {
        return {
          success: false,
          message: '未配置OSS存储，无法获取备份列表'
        };
      }

      // 更新OSS服务配置
      ossService.updateConfig(ossConfig);

      const result = await databaseSyncService.listDatabaseBackups();
      
      if (result.success) {
        console.log('✅ 获取备份列表成功，共', result.backups?.length || 0, '个备份');
      } else {
        console.error('❌ 获取备份列表失败:', result.message);
      }

      return result;
    } catch (error) {
      console.error('❌ 获取备份列表失败:', error);
      return {
        success: false,
        message: `获取备份列表失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 从OSS恢复数据库
  ipcMain.handle('restore-database-from-oss', async (event, backupName: string) => {
    try {
      console.log('📥 开始从OSS恢复数据库:', backupName);
      
      if (!backupName) {
        return {
          success: false,
          message: '备份文件名不能为空'
        };
      }

      // 确保OSS服务已配置
      const ossConfig = settingsService.getOSSConfig();
      if (!ossConfig) {
        return {
          success: false,
          message: '未配置OSS存储，无法执行恢复操作'
        };
      }

      // 更新OSS服务配置
      ossService.updateConfig(ossConfig);

      // 定义进度回调函数
      const progressCallback = (progress: number, message: string) => {
        event.sender.send('restore-progress', { progress, message });
      };

      const result = await databaseSyncService.restoreDatabaseFromOSS(backupName, progressCallback);
      
      if (result.success) {
        console.log('✅ 数据库恢复成功:', backupName);
        
        // 恢复成功后重新初始化服务以使用新数据库
        try {
          initializeServices();
          console.log('✅ 服务重新初始化完成');
          
          // 通知前端刷新界面
          console.log('🔄 通知前端刷新界面');
          event.sender.send('database-restored', { backupName });
          
          // 刷新主窗口
          if (mainWindow && !mainWindow.isDestroyed()) {
            console.log('🔄 刷新主窗口');
            mainWindow.reload();
          }
        } catch (initError) {
          console.warn('⚠️ 服务重新初始化失败:', initError);
        }
      } else {
        console.error('❌ 数据库恢复失败:', result.message);
      }

      return result;
    } catch (error) {
      console.error('❌ 从OSS恢复数据库失败:', error);
      return {
        success: false,
        message: `恢复失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 验证数据库备份（带内容预览）
  ipcMain.handle('validate-database-backup', async (_, backupName: string) => {
    try {
      console.log('🔍 开始验证备份文件:', backupName);
      
      if (!backupName) {
        return {
          success: false,
          message: '备份文件名不能为空'
        };
      }

      // 确保OSS服务已配置
      const ossConfig = settingsService.getOSSConfig();
      if (!ossConfig) {
        return {
          success: false,
          message: '未配置OSS存储，无法验证备份'
        };
      }

      // 更新OSS服务配置
      ossService.updateConfig(ossConfig);

      const result = await databaseSyncService.validateDatabaseBackup(backupName);
      
      console.log('✅ 备份文件验证完成:', backupName, result.valid ? '有效' : '无效');
      return result;
    } catch (error) {
      console.error('❌ 验证备份文件失败:', error);
      return {
        success: false,
        valid: false,
        message: `验证失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 获取数据库同步设置
  ipcMain.handle('get-database-sync-settings', () => {
    try {
      const syncStatus = settingsService.getDatabaseSyncStatus();
      console.log('📊 获取数据库同步设置:', syncStatus);
      return {
        success: true,
        data: syncStatus,
        message: '获取同步设置成功'
      };
    } catch (error) {
      console.error('❌ 获取数据库同步设置失败:', error);
      return {
        success: false,
        message: `获取设置失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 更新数据库同步设置
  ipcMain.handle('update-database-sync-settings', async (_, settings: { enableDatabaseSync?: boolean }) => {
    try {
      console.log('⚙️ 更新数据库同步设置:', settings);
      
      let updateResult = true;

      if (settings.enableDatabaseSync !== undefined) {
        updateResult = settingsService.setDatabaseSyncEnabled(settings.enableDatabaseSync);
      }

      if (!updateResult) {
        return {
          success: false,
          message: '保存设置失败'
        };
      }

      const syncStatus = settingsService.getDatabaseSyncStatus();
      
      return {
        success: true,
        data: syncStatus,
        message: '数据库同步设置已更新'
      };
    } catch (error) {
      console.error('❌ 更新数据库同步设置失败:', error);
      return {
        success: false,
        message: `更新设置失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 删除数据库备份
  ipcMain.handle('delete-database-backup', async (_, backupName: string) => {
    try {
      console.log('🗑️ 删除数据库备份:', backupName);
      
      const result = await databaseSyncService.deleteDatabaseBackup(backupName);
      
      if (result.success) {
        console.log('✅ 数据库备份删除成功:', backupName);
      } else {
        console.error('❌ 数据库备份删除失败:', result.message);
      }
      
      return result;
    } catch (error) {
      console.error('❌ 删除数据库备份失败:', error);
      return {
        success: false,
        message: `删除失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 重命名数据库备份
  ipcMain.handle('rename-database-backup', async (_, oldBackupName: string, newBackupName: string) => {
    try {
      console.log('📝 重命名数据库备份:', oldBackupName, '->', newBackupName);
      
      const result = await databaseSyncService.renameDatabaseBackup(oldBackupName, newBackupName);
      
      if (result.success) {
        console.log('✅ 数据库备份重命名成功:', oldBackupName, '->', newBackupName);
      } else {
        console.error('❌ 数据库备份重命名失败:', result.message);
      }
      
      return result;
    } catch (error) {
      console.error('❌ 重命名数据库备份失败:', error);
      return {
        success: false,
        message: `重命名失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // 文件夹导入相关IPC处理器
  ipcMain.handle('validate-folder-structure', async (_, folderPath: string) => {
    try {
      console.log('📁 验证文件夹结构:', folderPath);

      const result = await folderValidator.validateFolderStructure(folderPath);
      
      if (result.isValid) {
        console.log('✅ 文件夹结构验证成功:', result.categories.length, '个分类');
      } else {
        console.log('❌ 文件夹结构验证失败:', result.errors);
      }
      
      return result;
    } catch (error) {
      console.error('❌ 验证文件夹结构失败:', error);
      return {
        isValid: false,
        categories: [],
        errors: [error instanceof Error ? error.message : String(error)],
        warnings: []
      };
    }
  });

  ipcMain.handle('import-from-folder', async (_, folderPath: string, options: any) => {
    try {
      console.log('📥 开始文件夹导入:', folderPath);
      
      const result = await batchImportService.importFromFolder(
        folderPath,
        options,
        (progress) => {
          // 发送进度更新到渲染进程
          if (mainWindow) {
            mainWindow.webContents.send('import-progress', progress);
          }
        }
      );
      
      if (result.success) {
        console.log('✅ 文件夹导入成功:', result.importedCategories, '个分类,', result.importedImages, '张图片');
      } else {
        console.log('❌ 文件夹导入失败:', result.errors);
      }
      
      return result;
    } catch (error) {
      console.error('❌ 文件夹导入失败:', error);
      return {
        success: false,
        totalCategories: 0,
        importedCategories: 0,
        totalImages: 0,
        importedImages: 0,
        failedImages: 0,
        errors: [error instanceof Error ? error.message : String(error)],
        warnings: [],
        duration: 0
      };
    }
  });

  ipcMain.handle('select-import-folder', async () => {
    try {
      console.log('📂 选择导入文件夹');
      
      const result = await dialog.showOpenDialog(mainWindow, {
        title: '选择要导入的文件夹',
        message: '请选择包含分类文件夹的目录',
        properties: ['openDirectory'],
        buttonLabel: '选择文件夹'
      });
      
      if (result.canceled || !result.filePaths.length) {
        console.log('❌ 用户取消了文件夹选择');
        return { canceled: true };
      }
      
      const selectedPath = result.filePaths[0];
      console.log('✅ 选择的文件夹:', selectedPath);
      
      return { canceled: false, filePath: selectedPath };
    } catch (error) {
      console.error('❌ 选择文件夹失败:', error);
      return { canceled: true, error: error instanceof Error ? error.message : String(error) };
    }
  });
}

app.whenReady().then(() => {
  // 首先初始化日志系统
  initializeLogging();
  
  // 初始化服务
  initializeServices();
  
  // 注册IPC处理器
  registerIpcHandlers();
  
  // 然后创建窗口
  createWindow();
});

app.on('window-all-closed', () => {
  if (dbManager) {
    dbManager.close();
  }
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});