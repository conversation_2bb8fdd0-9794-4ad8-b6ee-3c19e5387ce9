import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand, HeadObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { OSSConfig } from './SettingsService';

export class OSSService {
  private s3Client: S3Client | null = null;
  private config: OSSConfig | null = null;

  constructor(config?: OSSConfig) {
    if (config) {
      this.updateConfig(config);
    }
  }

  /**
   * 更新OSS配置
   */
  updateConfig(config: OSSConfig): void {
    this.config = config;
    this.s3Client = new S3Client({
      endpoint: config.endpoint,
      region: config.region,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey
      },
      forcePathStyle: true, // 兼容OSS等S3兼容服务
      maxAttempts: 3        // 重试次数
    });
  }

  /**
   * 测试OSS连接
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      if (!this.s3Client || !this.config) {
        return { success: false, message: '未配置OSS连接' };
      }

      // 尝试列出bucket中的对象（限制1个）
      const command = new ListObjectsV2Command({
        Bucket: this.config.bucket,
        MaxKeys: 1
      });

      await this.s3Client.send(command);
      
      return { success: true, message: '连接成功' };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ OSS连接测试失败:', errorMessage);
      return { success: false, message: `连接失败: ${errorMessage}` };
    }
  }

  /**
   * 上传文件到OSS
   */
  async uploadFile(key: string, fileBuffer: Buffer, mimeType: string): Promise<{ success: boolean; url?: string; message: string }> {
    try {
      if (!this.s3Client || !this.config) {
        return { success: false, message: '未配置OSS连接' };
      }

      const fullKey = this.config.pathPrefix ? `${this.config.pathPrefix}/${key}` : key;

      // 使用lib-storage的Upload类进行分块上传
      const upload = new Upload({
        client: this.s3Client,
        params: {
          Bucket: this.config.bucket,
          Key: fullKey,
          Body: fileBuffer,
          ContentType: mimeType
        },
        // 分块上传配置
        queueSize: 4,
        partSize: 1024 * 1024 * 5, // 5MB per part
        leavePartsOnError: false
      });

      await upload.done();
      
      const url = `${this.config.endpoint}/${this.config.bucket}/${fullKey}`;
      return { success: true, url, message: '上传成功' };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 文件上传失败:', errorMessage);
      return { success: false, message: `上传失败: ${errorMessage}` };
    }
  }

  /**
   * 从OSS下载文件
   */
  async downloadFile(key: string): Promise<{ success: boolean; data?: Buffer; message: string }> {
    try {
      if (!this.s3Client || !this.config) {
        return { success: false, message: '未配置OSS连接' };
      }

      const fullKey = this.config.pathPrefix ? `${this.config.pathPrefix}/${key}` : key;

      const command = new GetObjectCommand({
        Bucket: this.config.bucket,
        Key: fullKey
      });

      const response = await this.s3Client.send(command);
      
      if (!response.Body) {
        return { success: false, message: '文件内容为空' };
      }

      // 将流转换为Buffer
      const chunks: Uint8Array[] = [];
      const stream = response.Body as any;
      
      for await (const chunk of stream) {
        chunks.push(chunk);
      }
      
      const data = Buffer.concat(chunks);
      return { success: true, data, message: '下载成功' };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 文件下载失败:', errorMessage);
      return { success: false, message: `下载失败: ${errorMessage}` };
    }
  }

  /**
   * 删除OSS文件
   */
  async deleteFile(key: string): Promise<{ success: boolean; message: string }> {
    try {
      if (!this.s3Client || !this.config) {
        return { success: false, message: '未配置OSS连接' };
      }

      const fullKey = this.config.pathPrefix ? `${this.config.pathPrefix}/${key}` : key;

      const command = new DeleteObjectCommand({
        Bucket: this.config.bucket,
        Key: fullKey
      });

      await this.s3Client.send(command);
      
      return { success: true, message: '删除成功' };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 文件删除失败:', errorMessage);
      return { success: false, message: `删除失败: ${errorMessage}` };
    }
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(key: string): Promise<{ success: boolean; exists: boolean; message: string }> {
    try {
      if (!this.s3Client || !this.config) {
        return { success: false, exists: false, message: '未配置OSS连接' };
      }

      const fullKey = this.config.pathPrefix ? `${this.config.pathPrefix}/${key}` : key;

      const command = new HeadObjectCommand({
        Bucket: this.config.bucket,
        Key: fullKey
      });

      await this.s3Client.send(command);
      
      return { success: true, exists: true, message: '文件存在' };
    } catch (error) {
      if (error instanceof Error && error.name === 'NotFound') {
        return { success: true, exists: false, message: '文件不存在' };
      }
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 检查文件失败:', errorMessage);
      return { success: false, exists: false, message: `检查失败: ${errorMessage}` };
    }
  }

  /**
   * 列出文件
   */
  async listFiles(prefix?: string, maxKeys: number = 1000): Promise<{ success: boolean; files?: string[]; message: string }> {
    try {
      if (!this.s3Client || !this.config) {
        return { success: false, message: '未配置OSS连接' };
      }

      const fullPrefix = this.config.pathPrefix 
        ? (prefix ? `${this.config.pathPrefix}/${prefix}` : this.config.pathPrefix)
        : prefix;

      const command = new ListObjectsV2Command({
        Bucket: this.config.bucket,
        Prefix: fullPrefix,
        MaxKeys: maxKeys
      });

      const response = await this.s3Client.send(command);
      
      const files = response.Contents?.map(obj => obj.Key || '') || [];
      return { success: true, files, message: '列表获取成功' };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 文件列表获取失败:', errorMessage);
      return { success: false, message: `列表获取失败: ${errorMessage}` };
    }
  }

  /**
   * 列出文件（包含元数据）
   */
  async listFilesWithMetadata(prefix?: string, maxKeys: number = 1000): Promise<{ 
    success: boolean; 
    files?: Array<{
      key: string;
      size: number;
      lastModified: string;
    }>; 
    message: string 
  }> {
    try {
      if (!this.s3Client || !this.config) {
        return { success: false, message: '未配置OSS连接' };
      }

      const fullPrefix = this.config.pathPrefix 
        ? (prefix ? `${this.config.pathPrefix}/${prefix}` : this.config.pathPrefix)
        : prefix;

      const command = new ListObjectsV2Command({
        Bucket: this.config.bucket,
        Prefix: fullPrefix,
        MaxKeys: maxKeys
      });

      const response = await this.s3Client.send(command);
      
      const files = response.Contents?.map(obj => ({
        key: obj.Key || '',
        size: obj.Size || 0,
        lastModified: obj.LastModified?.toISOString() || new Date().toISOString()
      })) || [];
      
      return { success: true, files, message: '列表获取成功' };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 文件列表获取失败:', errorMessage);
      return { success: false, message: `列表获取失败: ${errorMessage}` };
    }
  }

  /**
   * 生成签名URL（用于临时访问）
   */
  async generateSignedUrl(key: string, expiresIn: number = 3600): Promise<{ success: boolean; url?: string; message: string }> {
    try {
      if (!this.s3Client || !this.config) {
        return { success: false, message: '未配置OSS连接' };
      }

      const fullKey = this.config.pathPrefix ? `${this.config.pathPrefix}/${key}` : key;

      const command = new GetObjectCommand({
        Bucket: this.config.bucket,
        Key: fullKey
      });

      const url = await getSignedUrl(this.s3Client, command, { expiresIn });
      
      return { success: true, url, message: '签名URL生成成功' };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 签名URL生成失败:', errorMessage);
      return { success: false, message: `签名URL生成失败: ${errorMessage}` };
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): OSSConfig | null {
    return this.config;
  }

  /**
   * 检查是否已配置
   */
  isConfigured(): boolean {
    return !!(this.config && this.s3Client);
  }

  /**
   * 销毁客户端连接
   */
  destroy(): void {
    if (this.s3Client) {
      this.s3Client.destroy();
      this.s3Client = null;
    }
    this.config = null;
  }
}