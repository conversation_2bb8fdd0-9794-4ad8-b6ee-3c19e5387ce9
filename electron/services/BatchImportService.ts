import { DatabaseManager } from '../database';
import { CategoryService } from './CategoryService';
import { ImageService } from './ImageService';
import { FolderStructureValidator } from './FolderStructureValidator';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

/**
 * 导入选项接口
 */
export interface ImportOptions {
  overwriteExisting: boolean;      // 是否覆盖现有数据
  compressImages: boolean;         // 是否压缩图片
  compressionQuality: number;      // 压缩质量 (0-100)
  sanitizeCategoryNames: boolean;  // 是否清理分类名称
  createThumbnails: boolean;       // 是否创建缩略图
  preserveFileNames: boolean;      // 是否保留原文件名
  
  // 性能优化选项
  maxConcurrentUploads?: number;   // 最大并发上传数 (默认: 3)
  batchSize?: number;              // 分批处理大小 (默认: 50)
  enableProgressiveUpload?: boolean; // 启用渐进式上传 (默认: true)
  memoryLimitMB?: number;          // 内存限制MB (默认: 100)
}

/**
 * 导入进度接口
 */
export interface ImportProgress {
  stage: 'validation' | 'reset' | 'categories' | 'images' | 'complete';
  progress: number;                // 0-100
  currentCategory?: string;
  currentImage?: string;
  totalCategories: number;
  completedCategories: number;
  totalImages: number;
  completedImages: number;
  errors: string[];
}

/**
 * 导入结果接口
 */
export interface ImportResult {
  success: boolean;
  totalCategories: number;
  importedCategories: number;
  totalImages: number;
  importedImages: number;
  failedImages: number;
  errors: string[];
  warnings: string[];
  duration: number;
}

/**
 * 批量导入服务
 */
export class BatchImportService {
  private categoryService: CategoryService;
  private imageService: ImageService;
  private folderValidator: FolderStructureValidator;

  // 异步文件操作
  private readFile = promisify(fs.readFile);
  private readdir = promisify(fs.readdir);
  private stat = promisify(fs.stat);

  // 性能优化默认配置
  private readonly DEFAULT_MAX_CONCURRENT = 3;
  private readonly DEFAULT_BATCH_SIZE = 50;
  private readonly DEFAULT_MEMORY_LIMIT_MB = 100;

  constructor(private dbManager: DatabaseManager) {
    this.categoryService = new CategoryService(dbManager);
    this.imageService = new ImageService(dbManager);
    this.folderValidator = new FolderStructureValidator();
  }

  /**
   * 从文件夹导入数据
   */
  async importFromFolder(
    folderPath: string,
    options: ImportOptions,
    progressCallback?: (progress: ImportProgress) => void
  ): Promise<ImportResult> {
    const startTime = Date.now();
    const result: ImportResult = {
      success: false,
      totalCategories: 0,
      importedCategories: 0,
      totalImages: 0,
      importedImages: 0,
      failedImages: 0,
      errors: [],
      warnings: [],
      duration: 0,
    };

    try {
      // 1. 验证文件夹结构
      this.reportProgress(progressCallback, {
        stage: 'validation',
        progress: 10,
        totalCategories: 0,
        completedCategories: 0,
        totalImages: 0,
        completedImages: 0,
        errors: [],
      });

      const validationResult = await this.folderValidator.validateFolderStructure(folderPath);
      
      if (!validationResult.isValid) {
        result.errors.push(`文件夹结构验证失败: ${validationResult.errors.join(', ')}`);
        result.duration = Date.now() - startTime;
        return result;
      }

      result.totalCategories = validationResult.categories.length;
      result.totalImages = validationResult.categories.reduce((sum, cat) => sum + cat.imageCount, 0);
      result.warnings.push(...validationResult.warnings);

      // 2. 重置数据库
      this.reportProgress(progressCallback, {
        stage: 'reset',
        progress: 20,
        totalCategories: result.totalCategories,
        completedCategories: 0,
        totalImages: result.totalImages,
        completedImages: 0,
        errors: [],
      });

      const resetResult = await this.dbManager.resetDatabase();
      if (!resetResult.success) {
        result.errors.push(`数据库重置失败: ${resetResult.message}`);
        result.duration = Date.now() - startTime;
        return result;
      }

      // 3. 创建分类
      this.reportProgress(progressCallback, {
        stage: 'categories',
        progress: 30,
        totalCategories: result.totalCategories,
        completedCategories: 0,
        totalImages: result.totalImages,
        completedImages: 0,
        errors: [],
      });

      const categoryMap = new Map<string, string>(); // categoryName -> categoryId
      
      for (let i = 0; i < validationResult.categories.length; i++) {
        const categoryInfo = validationResult.categories[i];
        
        try {
          const categoryData = {
            name: options.sanitizeCategoryNames ? this.sanitizeCategoryName(categoryInfo.name) : categoryInfo.name,
            description: null,
          };

          const createdCategory = await this.categoryService.createCategory(categoryData);
          categoryMap.set(categoryInfo.name, createdCategory.id);
          result.importedCategories++;

          this.reportProgress(progressCallback, {
            stage: 'categories',
            progress: 30 + (i + 1) / validationResult.categories.length * 20,
            currentCategory: categoryInfo.name,
            totalCategories: result.totalCategories,
            completedCategories: result.importedCategories,
            totalImages: result.totalImages,
            completedImages: 0,
            errors: [],
          });
        } catch (error) {
          result.errors.push(`创建分类 "${categoryInfo.name}" 失败: ${error instanceof Error ? error.message : String(error)}`);
          result.duration = Date.now() - startTime;
          return result;
        }
      }

      // 4. 导入图片（优化版本）
      this.reportProgress(progressCallback, {
        stage: 'images',
        progress: 50,
        totalCategories: result.totalCategories,
        completedCategories: result.importedCategories,
        totalImages: result.totalImages,
        completedImages: 0,
        errors: [],
      });

      // 使用优化的并发上传方法
      const imageUploadResult = await this.uploadImagesOptimized(
        validationResult.categories,
        categoryMap,
        options,
        progressCallback,
        result
      );

      result.importedImages = imageUploadResult.importedImages;
      result.failedImages = imageUploadResult.failedImages;
      result.warnings.push(...imageUploadResult.warnings);

      // 5. 完成
      this.reportProgress(progressCallback, {
        stage: 'complete',
        progress: 100,
        totalCategories: result.totalCategories,
        completedCategories: result.importedCategories,
        totalImages: result.totalImages,
        completedImages: imageUploadResult.totalProcessed,
        errors: [],
      });

      result.success = true;
      result.duration = Date.now() - startTime;
      return result;

    } catch (error) {
      result.errors.push(`导入过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
      result.duration = Date.now() - startTime;
      return result;
    }
  }

  /**
   * 报告进度
   */
  private reportProgress(
    callback: ((progress: ImportProgress) => void) | undefined,
    progress: ImportProgress
  ): void {
    if (callback) {
      callback(progress);
    }
  }

  /**
   * 清理分类名称
   */
  private sanitizeCategoryName(name: string): string {
    return name.replace(/[<>:"/\\|?*]/g, '').trim();
  }

  /**
   * 获取MIME类型
   */
  private getMimeType(extension: string): string {
    const mimeTypes: { [key: string]: string } = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',
    };

    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * 优化的并发图片上传方法
   */
  private async uploadImagesOptimized(
    categories: Array<{ name: string; imageCount: number; path: string }>,
    categoryMap: Map<string, string>,
    options: ImportOptions,
    progressCallback?: (progress: ImportProgress) => void,
    result: ImportResult
  ): Promise<{
    importedImages: number;
    failedImages: number;
    totalProcessed: number;
    warnings: string[];
  }> {
    const maxConcurrent = options.maxConcurrentUploads || this.DEFAULT_MAX_CONCURRENT;
    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    
    let importedImages = 0;
    let failedImages = 0;
    let totalProcessed = 0;
    const warnings: string[] = [];

    // 预收集所有图片信息
    const allImageTasks: Array<{
      categoryId: string;
      categoryName: string;
      imagePath: string;
      fileName: string;
      mimeType: string;
    }> = [];

    for (const categoryInfo of categories) {
      const categoryId = categoryMap.get(categoryInfo.name);
      if (!categoryId) continue;

      try {
        const imageFiles = await this.readdir(categoryInfo.path);
        const supportedFiles = imageFiles.filter(file => 
          this.folderValidator.isSupportedImageFormat(file)
        );

        for (const imageFile of supportedFiles) {
          const imagePath = path.join(categoryInfo.path, imageFile);
          const mimeType = this.getMimeType(path.extname(imageFile));
          
          allImageTasks.push({
            categoryId,
            categoryName: categoryInfo.name,
            imagePath,
            fileName: imageFile,
            mimeType,
          });
        }
      } catch (error) {
        warnings.push(`读取分类 "${categoryInfo.name}" 的文件时失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // 分批并发处理
    for (let i = 0; i < allImageTasks.length; i += batchSize) {
      const batch = allImageTasks.slice(i, i + batchSize);
      
      const batchResults = await this.processBatchWithConcurrency(
        batch,
        maxConcurrent,
        options
      );

      importedImages += batchResults.imported;
      failedImages += batchResults.failed;
      totalProcessed += batchResults.processed;
      warnings.push(...batchResults.warnings);

      // 报告进度
      this.reportProgress(progressCallback, {
        stage: 'images',
        progress: 50 + (totalProcessed / result.totalImages) * 40,
        currentCategory: batch[batch.length - 1]?.categoryName,
        currentImage: batch[batch.length - 1]?.fileName,
        totalCategories: result.totalCategories,
        completedCategories: result.importedCategories,
        totalImages: result.totalImages,
        completedImages: totalProcessed,
        errors: [],
      });

      // 内存管理：在批次之间稍作暂停
      if (i + batchSize < allImageTasks.length) {
        await this.delay(10); // 10ms暂停
      }
    }

    return {
      importedImages,
      failedImages,
      totalProcessed,
      warnings,
    };
  }

  /**
   * 使用并发控制处理单个批次
   */
  private async processBatchWithConcurrency(
    batch: Array<{
      categoryId: string;
      categoryName: string;
      imagePath: string;
      fileName: string;
      mimeType: string;
    }>,
    maxConcurrent: number,
    options: ImportOptions
  ): Promise<{
    imported: number;
    failed: number;
    processed: number;
    warnings: string[];
  }> {
    let imported = 0;
    let failed = 0;
    let processed = 0;
    const warnings: string[] = [];

    // 使用信号量控制并发数
    const semaphore = new Array(maxConcurrent).fill(null);
    const tasks: Promise<void>[] = [];

    for (const task of batch) {
      const uploadTask = this.acquireSlot(semaphore).then(async (releaseSlot) => {
        try {
          await this.uploadSingleImage(task, options);
          imported++;
        } catch (error) {
          failed++;
          warnings.push(`图片上传失败: ${task.fileName} - ${error instanceof Error ? error.message : String(error)}`);
        } finally {
          processed++;
          releaseSlot();
        }
      });

      tasks.push(uploadTask);
    }

    await Promise.all(tasks);

    return {
      imported,
      failed,
      processed,
      warnings,
    };
  }

  /**
   * 上传单张图片
   */
  private async uploadSingleImage(
    task: {
      categoryId: string;
      categoryName: string;
      imagePath: string;
      fileName: string;
      mimeType: string;
    },
    options: ImportOptions
  ): Promise<void> {
    // 检查文件大小以控制内存使用
    const stats = await this.stat(task.imagePath);
    const fileSizeMB = stats.size / (1024 * 1024);
    
    if (fileSizeMB > (options.memoryLimitMB || this.DEFAULT_MEMORY_LIMIT_MB)) {
      throw new Error(`文件过大: ${fileSizeMB.toFixed(2)}MB，超过限制 ${options.memoryLimitMB || this.DEFAULT_MEMORY_LIMIT_MB}MB`);
    }

    const imageBuffer = await this.readFile(task.imagePath);
    
    await this.imageService.uploadImage(
      task.categoryId,
      imageBuffer,
      task.fileName,
      task.mimeType,
      false // setAsCategoryThumbnail
    );
  }

  /**
   * 信号量实现：获取可用槽位
   */
  private async acquireSlot(semaphore: any[]): Promise<() => void> {
    return new Promise((resolve) => {
      const tryAcquire = () => {
        for (let i = 0; i < semaphore.length; i++) {
          if (semaphore[i] === null) {
            semaphore[i] = true;
            resolve(() => {
              semaphore[i] = null;
            });
            return;
          }
        }
        // 如果没有可用槽位，使用setImmediate避免阻塞
        setImmediate(tryAcquire);
      };
      tryAcquire();
    });
  }

  /**
   * 延迟工具函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}