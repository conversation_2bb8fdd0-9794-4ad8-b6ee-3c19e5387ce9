import * as fs from 'fs';
import * as path from 'path';

/**
 * 文件夹验证结果接口
 */
export interface FolderValidationResult {
  isValid: boolean;
  categories: Array<{
    name: string;
    imageCount: number;
    path: string;
  }>;
  errors: string[];
  warnings: string[];
}

/**
 * 支持的图片格式
 */
const SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];

/**
 * 验证规则
 */
const VALIDATION_RULES = {
  minCategories: 1,
  maxCategories: 100,
  minImagesPerCategory: 1,
  maxImagesPerCategory: 10000,
  maxCategoryNameLength: 50
};

/**
 * 文件夹结构验证服务
 */
export class FolderStructureValidator {
  
  /**
   * 验证文件夹结构是否符合导入要求
   * @param folderPath 要验证的文件夹路径
   * @returns 验证结果
   */
  async validateFolderStructure(folderPath: string): Promise<FolderValidationResult> {
    console.log('🔍 [FolderValidator] 开始验证文件夹结构:', folderPath);

    const result: FolderValidationResult = {
      isValid: false,
      categories: [],
      errors: [],
      warnings: []
    };

    try {
      // 1. 检查文件夹是否存在
      console.log('📂 [FolderValidator] 检查文件夹是否存在:', folderPath);
      if (!fs.existsSync(folderPath)) {
        console.error('❌ [FolderValidator] 文件夹不存在:', folderPath);
        result.errors.push('文件夹不存在');
        return result;
      }
      console.log('✅ [FolderValidator] 文件夹存在');

      // 2. 检查是否是目录
      console.log('📁 [FolderValidator] 检查是否为目录');
      const folderStats = fs.statSync(folderPath);
      if (!folderStats.isDirectory()) {
        console.error('❌ [FolderValidator] 路径不是一个文件夹');
        result.errors.push('路径不是一个文件夹');
        return result;
      }
      console.log('✅ [FolderValidator] 确认为目录');

      // 3. 读取文件夹内容
      console.log('📋 [FolderValidator] 读取文件夹内容');
      const folderContents = fs.readdirSync(folderPath);
      console.log('📋 [FolderValidator] 文件夹内容:', folderContents);

      if (folderContents.length === 0) {
        console.error('❌ [FolderValidator] 文件夹为空');
        result.errors.push('文件夹为空，没有找到任何分类文件夹');
        return result;
      }

      // 4. 过滤出子文件夹（分类文件夹）
      const categoryFolders = folderContents.filter(item => {
        const itemPath = path.join(folderPath, item);
        try {
          const itemStats = fs.statSync(itemPath);
          return itemStats.isDirectory();
        } catch (error) {
          result.warnings.push(`无法访问 "${item}": ${error instanceof Error ? error.message : String(error)}`);
          return false;
        }
      });

      if (categoryFolders.length === 0) {
        result.errors.push('没有找到任何分类文件夹');
        return result;
      }

      // 5. 检查分类数量限制
      if (categoryFolders.length > VALIDATION_RULES.maxCategories) {
        result.errors.push(`分类数量 (${categoryFolders.length}) 超过最大限制 (${VALIDATION_RULES.maxCategories})`);
        return result;
      }

      // 6. 验证每个分类文件夹
      for (const categoryFolder of categoryFolders) {
        const categoryPath = path.join(folderPath, categoryFolder);
        
        // 验证分类名称长度
        if (categoryFolder.length > VALIDATION_RULES.maxCategoryNameLength) {
          result.errors.push(`分类名称 "${categoryFolder}" 超过最大长度限制 (${VALIDATION_RULES.maxCategoryNameLength} 字符)`);
          continue;
        }

        // 读取分类文件夹内容
        let categoryContents: string[] = [];
        try {
          categoryContents = fs.readdirSync(categoryPath);
        } catch (error) {
          result.errors.push(`无法读取分类文件夹 "${categoryFolder}": ${error instanceof Error ? error.message : String(error)}`);
          continue;
        }

        // 过滤出支持的图片文件
        const imageFiles = categoryContents.filter(file => {
          const filePath = path.join(categoryPath, file);
          try {
            const fileStats = fs.statSync(filePath);
            if (!fileStats.isFile()) {
              return false;
            }
            
            const fileExt = path.extname(file).toLowerCase();
            const isSupported = SUPPORTED_IMAGE_FORMATS.includes(fileExt);
            
            if (!isSupported) {
              result.warnings.push(`文件 "${file}" 不是支持的图片格式，将被跳过`);
            }
            
            return isSupported;
          } catch (error) {
            result.warnings.push(`无法访问文件 "${file}": ${error instanceof Error ? error.message : String(error)}`);
            return false;
          }
        });

        // 检查是否包含图片
        if (imageFiles.length === 0) {
          result.errors.push(`分类文件夹 "${categoryFolder}" 中没有找到任何图片`);
          continue;
        }

        // 检查图片数量限制
        if (imageFiles.length > VALIDATION_RULES.maxImagesPerCategory) {
          result.errors.push(`分类 "${categoryFolder}" 中的图片数量 (${imageFiles.length}) 超过最大限制 (${VALIDATION_RULES.maxImagesPerCategory})`);
          continue;
        }

        // 添加到结果中
        result.categories.push({
          name: categoryFolder,
          imageCount: imageFiles.length,
          path: categoryPath
        });
      }

      // 7. 最终验证
      if (result.categories.length === 0) {
        result.errors.push('没有找到任何有效的分类文件夹');
      } else if (result.categories.length < VALIDATION_RULES.minCategories) {
        result.errors.push(`有效分类数量 (${result.categories.length}) 少于最小要求 (${VALIDATION_RULES.minCategories})`);
      } else {
        result.isValid = true;
      }

    } catch (error) {
      result.errors.push(`文件系统错误: ${error instanceof Error ? error.message : String(error)}`);
    }

    return result;
  }

  /**
   * 检查文件是否是支持的图片格式
   * @param filename 文件名
   * @returns 是否支持
   */
  isSupportedImageFormat(filename: string): boolean {
    const ext = path.extname(filename).toLowerCase();
    return SUPPORTED_IMAGE_FORMATS.includes(ext);
  }

  /**
   * 获取支持的图片格式列表
   * @returns 支持的格式数组
   */
  getSupportedFormats(): string[] {
    return [...SUPPORTED_IMAGE_FORMATS];
  }

  /**
   * 获取验证规则
   * @returns 验证规则对象
   */
  getValidationRules() {
    return { ...VALIDATION_RULES };
  }
}