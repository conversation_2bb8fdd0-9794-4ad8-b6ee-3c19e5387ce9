import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import Database from 'better-sqlite3';
import { OSSService } from './OSSService';
import { SettingsService } from './SettingsService';
import { DatabaseManager } from '../database';

// 数据库备份信息接口
interface DatabaseBackupInfo {
  name: string;                       // 备份文件名（包含时间信息）
  size: number;                       // 文件大小（字节）
  lastModified: string;               // 最后修改时间（从OSS获取）
}

// 数据库同步操作结果接口
interface DatabaseSyncResult {
  success: boolean;
  message: string;
  backupInfo?: DatabaseBackupInfo;
  error?: string;
}

// 备份列表结果接口
interface BackupListResult {
  success: boolean;
  backups?: DatabaseBackupInfo[];
  message: string;
}

// 恢复操作结果接口
interface RestoreResult {
  success: boolean;
  message: string;
  error?: string;
}

export class DatabaseSyncService {
  private ossService: OSSService;
  private settingsService: SettingsService;
  private databaseManager: DatabaseManager;

  constructor(ossService: OSSService, settingsService: SettingsService, databaseManager: DatabaseManager) {
    this.ossService = ossService;
    this.settingsService = settingsService;
    this.databaseManager = databaseManager;
  }

  /**
   * 备份数据库到OSS
   */
  async backupDatabaseToOSS(customName?: string, progressCallback?: (progress: number, message: string) => void): Promise<DatabaseSyncResult> {
    try {
      // 检查OSS是否已配置
      if (!this.ossService.isConfigured()) {
        return {
          success: false,
          message: '未配置OSS存储，无法执行备份操作'
        };
      }

      // 获取数据库文件路径
      const dbPath = this.databaseManager.getDatabasePath();
      
      if (!fs.existsSync(dbPath)) {
        console.error('❌ 数据库文件不存在:', dbPath);
        return {
          success: false,
          message: '数据库文件不存在'
        };
      }

      // 检查文件权限
      try {
        fs.accessSync(dbPath, fs.constants.R_OK);
      } catch (error) {
        console.error('❌ 数据库文件无法读取:', dbPath, error);
        return {
          success: false,
          message: '数据库文件无法读取'
        };
      }

      // 生成备份文件名
      const backupName = customName || this.generateBackupName();
      const ossKey = `databases/${backupName}`;


      // 进度回调
      progressCallback?.(10, '正在准备数据库备份...');

      // ⚠️ 关键修复：强制WAL检查点，确保所有数据写入主数据库文件
      try {
        const db = this.databaseManager.getDatabase() as any;
        if (db && typeof db.pragma === 'function') {
          db.pragma('wal_checkpoint(FULL)');
        } else {
        }
      } catch (walError) {
        // 继续执行备份，但记录警告
      }

      progressCallback?.(15, '正在读取数据库文件...');

      // 读取数据库文件
      const fileBuffer = fs.readFileSync(dbPath);
      const fileSize = fileBuffer.length;
      
      // 先验证活跃数据库连接中的数据
      try {
        const db = this.databaseManager.getDatabase() as any;
        if (db) {
          const liveCategories = db.prepare('SELECT id, name FROM categories ORDER BY created_at DESC').all();
        }
      } catch (error) {
      }

      // 然后验证读取的数据库文件内容
      try {
        const fileData = await this.previewBackupCategories(fileBuffer);
        
        // 比较两者差异
        const db = this.databaseManager.getDatabase() as any;
        if (db) {
          const liveCategories = db.prepare('SELECT id, name FROM categories ORDER BY created_at DESC').all();
          if (liveCategories.length !== fileData.length) {
            console.error('🚨 严重问题：活跃数据库和文件数据不一致！');
            console.error('🔴 活跃数据库分类数:', liveCategories.length);
            console.error('🔴 文件数据分类数:', fileData.length);
            console.error('🔴 活跃数据库分类:', liveCategories.map((c: any) => c.name));
            console.error('🔴 文件数据分类:', fileData.map((c: any) => c.name));
            
            return {
              success: false,
              message: `数据不一致：活跃数据库有${liveCategories.length}个分类，但文件只有${fileData.length}个分类`
            };
          }
        }
      } catch (error) {
      }

      progressCallback?.(20, '正在生成文件校验和...');

      // 生成文件校验和（用于完整性验证）
      const checksum = crypto.createHash('sha256').update(fileBuffer).digest('hex');

      progressCallback?.(40, '正在验证备份数据...');

      // 验证备份数据的完整性
      const validationResult = await this.validateBackupData(fileBuffer);
      if (!validationResult.isValid) {
        return {
          success: false,
          message: `备份数据验证失败: ${validationResult.error}`
        };
      }


      progressCallback?.(60, '正在上传到OSS...');

      // 上传到OSS（带重试机制）
      const uploadResult = await this.uploadWithRetry(ossKey, fileBuffer, 'application/x-sqlite3', progressCallback);

      if (!uploadResult.success) {
        return {
          success: false,
          message: `备份上传失败: ${uploadResult.message}`
        };
      }

      progressCallback?.(100, '备份完成');

      // 创建备份信息
      const backupInfo: DatabaseBackupInfo = {
        name: backupName,
        size: fileSize,
        lastModified: new Date().toISOString()
      };

      // 更新最后备份时间
      this.settingsService.saveSettings({
        lastBackupTime: new Date().toISOString()
      });


      return {
        success: true,
        message: '数据库备份成功',
        backupInfo
      };

    } catch (error) {
      console.error('❌ 数据库备份失败:', error);
      return {
        success: false,
        message: `数据库备份失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 列出OSS上的数据库备份
   */
  async listDatabaseBackups(): Promise<BackupListResult> {
    try {
      // 检查OSS是否已配置
      if (!this.ossService.isConfigured()) {
        return {
          success: false,
          message: '未配置OSS存储，无法获取备份列表'
        };
      }


      // 获取OSS上的数据库备份文件列表（包含元数据）
      const listResult = await this.ossService.listFilesWithMetadata('databases/', 1000);

      if (!listResult.success) {
        return {
          success: false,
          message: `获取备份列表失败: ${listResult.message}`
        };
      }

      const files = listResult.files || [];
      const backups: DatabaseBackupInfo[] = [];

      // 处理每个备份文件
      for (const file of files) {
        
        // 跳过目录
        if (file.key.endsWith('/')) {
          continue;
        }

        // 提取文件名（移除路径前缀）
        const fileName = path.basename(file.key);
        
        // 验证是否是数据库备份文件
        if (!fileName.startsWith('backup-') || !fileName.endsWith('.db')) {
          continue;
        }

        try {
          // 创建备份信息，优先使用OSS的真实LastModified时间
          const backup: DatabaseBackupInfo = {
            name: fileName,
            size: file.size,
            lastModified: file.lastModified // 使用OSS的真实最后修改时间
          };
          backups.push(backup);
        } catch (error) {
          console.error('处理备份文件时出错:', fileName, error);
        }
      }

      // 按时间倒序排列（最新的在前）
      backups.sort((a, b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime());


      return {
        success: true,
        backups,
        message: `找到 ${backups.length} 个数据库备份`
      };

    } catch (error) {
      console.error('❌ 获取备份列表失败:', error);
      return {
        success: false,
        message: `获取备份列表失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 从OSS恢复数据库
   */
  async restoreDatabaseFromOSS(backupName: string, progressCallback?: (progress: number, message: string) => void): Promise<RestoreResult> {
    try {
      // 检查OSS是否已配置
      if (!this.ossService.isConfigured()) {
        return {
          success: false,
          message: '未配置OSS存储，无法执行恢复操作'
        };
      }


      progressCallback?.(10, '正在检查备份文件...');

      const ossKey = `databases/${backupName}`;

      // 检查备份文件是否存在
      const existsResult = await this.ossService.fileExists(ossKey);
      if (!existsResult.success || !existsResult.exists) {
        return {
          success: false,
          message: '指定的备份文件不存在'
        };
      }

      progressCallback?.(20, '正在下载备份文件...');

      // 从OSS下载备份文件（带重试机制）
      const downloadResult = await this.downloadWithRetry(ossKey, progressCallback);
      if (!downloadResult.success || !downloadResult.data) {
        return {
          success: false,
          message: `下载备份文件失败: ${downloadResult.message}`
        };
      }


      progressCallback?.(60, '正在验证备份数据...');

      // 验证下载的备份数据
      const validationResult = await this.validateBackupData(downloadResult.data);
      if (!validationResult.isValid) {
        return {
          success: false,
          message: `备份数据验证失败: ${validationResult.error}`
        };
      }


      progressCallback?.(80, '正在恢复数据库...');

      // 执行数据库恢复
      const restoreResult = await this.databaseManager.restoreFromBackup(downloadResult.data);
      if (!restoreResult.success) {
        return {
          success: false,
          message: `数据库恢复失败: ${restoreResult.message}`
        };
      }

      progressCallback?.(90, '正在更新设置...');

      // 更新最后恢复时间
      this.settingsService.saveSettings({
        lastRestoreTime: new Date().toISOString()
      });

      progressCallback?.(100, '恢复完成');


      return {
        success: true,
        message: '数据库恢复成功'
      };

    } catch (error) {
      console.error('❌ 数据库恢复失败:', error);
      return {
        success: false,
        message: `数据库恢复失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 验证数据库备份文件
   */
  async validateDatabaseBackup(backupName: string): Promise<{ success: boolean; valid: boolean; message: string; details?: any }> {
    try {
      // 检查OSS是否已配置
      if (!this.ossService.isConfigured()) {
        return {
          success: false,
          valid: false,
          message: '未配置OSS存储'
        };
      }

      const ossKey = `databases/${backupName}`;


      // 检查文件是否存在
      const existsResult = await this.ossService.fileExists(ossKey);
      if (!existsResult.success) {
        return {
          success: false,
          valid: false,
          message: `验证失败: ${existsResult.message}`
        };
      }

      if (!existsResult.exists) {
        return {
          success: true,
          valid: false,
          message: '备份文件不存在'
        };
      }

      // 下载文件进行验证
      const downloadResult = await this.ossService.downloadFile(ossKey);
      if (!downloadResult.success || !downloadResult.data) {
        return {
          success: true,
          valid: false,
          message: '无法下载备份文件进行验证'
        };
      }

      const backupData = downloadResult.data;
      const validationDetails: any = {
        fileSize: backupData.length,
        checksum: null,
        sqliteVersion: null,
        tableCount: 0,
        validationTests: []
      };

      // 1. 验证SQLite文件格式
      const isValidSQLite = this.validateSQLiteFile(backupData);
      validationDetails.validationTests.push({
        test: 'SQLite格式验证',
        passed: isValidSQLite,
        message: isValidSQLite ? 'SQLite文件头正确' : 'SQLite文件头无效'
      });

      if (!isValidSQLite) {
        return {
          success: true,
          valid: false,
          message: '备份文件格式无效，不是有效的SQLite数据库',
          details: validationDetails
        };
      }

      // 2. 生成文件校验和
      try {
        validationDetails.checksum = crypto.createHash('sha256').update(backupData).digest('hex');
        validationDetails.validationTests.push({
          test: '文件校验和生成',
          passed: true,
          message: `SHA256: ${validationDetails.checksum.substring(0, 16)}...`
        });
      } catch (error) {
        validationDetails.validationTests.push({
          test: '文件校验和生成',
          passed: false,
          message: '校验和生成失败'
        });
      }

      // 3. 尝试打开数据库进行结构验证
      try {
        const validationResult = await this.validateDatabaseStructure(backupData);
        validationDetails.tableCount = validationResult.tableCount;
        validationDetails.sqliteVersion = validationResult.sqliteVersion;
        
        validationDetails.validationTests.push({
          test: '数据库结构验证',
          passed: validationResult.isValid,
          message: validationResult.isValid 
            ? `包含 ${validationResult.tableCount} 个表` 
            : validationResult.error || '数据库结构验证失败'
        });

        // 4. 验证必要的表是否存在
        const requiredTables = ['categories', 'images', 'tags', 'image_tags'];
        const missingTables = requiredTables.filter(table => !validationResult.tables.includes(table));
        
        const tablesValid = missingTables.length === 0;
        validationDetails.validationTests.push({
          test: '必要表结构检查',
          passed: tablesValid,
          message: tablesValid 
            ? '所有必要表都存在' 
            : `缺少表: ${missingTables.join(', ')}`
        });

        // 5. 验证分类数据内容
        try {
          const categoriesData = await this.previewBackupCategories(backupData);
          validationDetails.validationTests.push({
            test: '分类数据预览',
            passed: true,
            message: `包含 ${categoriesData.length} 个分类: ${categoriesData.map(c => c.name).join(', ')}`
          });
          validationDetails.categoriesPreview = categoriesData;
        } catch (error) {
          validationDetails.validationTests.push({
            test: '分类数据预览',
            passed: false,
            message: '无法读取分类数据'
          });
        }

        // 综合验证结果
        const allTestsPassed = validationDetails.validationTests.every((test: any) => test.passed);
        

        return {
          success: true,
          valid: allTestsPassed,
          message: allTestsPassed ? '备份文件验证通过' : '备份文件验证失败',
          details: validationDetails
        };

      } catch (structureError) {
        validationDetails.validationTests.push({
          test: '数据库结构验证',
          passed: false,
          message: `结构验证失败: ${structureError instanceof Error ? structureError.message : String(structureError)}`
        });

        return {
          success: true,
          valid: false,
          message: '数据库结构验证失败',
          details: validationDetails
        };
      }

    } catch (error) {
      console.error('❌ 验证备份文件失败:', error);
      return {
        success: false,
        valid: false,
        message: `验证失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 验证备份数据的完整性（在备份前）
   */
  private async validateBackupData(backupData: Buffer): Promise<{
    isValid: boolean;
    error?: string;
    summary?: string;
  }> {
    try {
      // 1. 验证SQLite文件格式
      if (!this.validateSQLiteFile(backupData)) {
        return {
          isValid: false,
          error: '文件不是有效的SQLite格式'
        };
      }

      // 2. 验证数据库结构
      const structureResult = await this.validateDatabaseStructure(backupData);
      if (!structureResult.isValid) {
        return {
          isValid: false,
          error: structureResult.error || '数据库结构验证失败'
        };
      }

      // 3. 检查必要的表
      const requiredTables = ['categories', 'images', 'tags', 'image_tags'];
      const missingTables = requiredTables.filter(table => !structureResult.tables.includes(table));
      
      if (missingTables.length > 0) {
        return {
          isValid: false,
          error: `缺少必要的表: ${missingTables.join(', ')}`
        };
      }

      const summary = `包含${structureResult.tableCount}个表，SQLite版本: ${structureResult.sqliteVersion || '未知'}`;

      return {
        isValid: true,
        summary
      };

    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 验证数据库结构
   */
  private async validateDatabaseStructure(backupData: Buffer): Promise<{
    isValid: boolean;
    tableCount: number;
    tables: string[];
    sqliteVersion?: string;
    error?: string;
  }> {
    const tempDbPath = `${this.databaseManager.getDatabasePath()}.temp-validation-${Date.now()}`;
    
    try {
      // 将备份数据写入临时文件
      fs.writeFileSync(tempDbPath, backupData);

      // 尝试打开数据库
      const tempDb = new Database(tempDbPath, { readonly: true });

      try {
        // 获取SQLite版本
        const versionResult = tempDb.prepare('SELECT sqlite_version() as version').get() as { version: string };
        const sqliteVersion = versionResult?.version;

        // 获取所有表名
        const tablesResult = tempDb.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name NOT LIKE 'sqlite_%'
          ORDER BY name
        `).all() as { name: string }[];

        const tables = tablesResult.map(row => row.name);
        const tableCount = tables.length;

        // 验证每个表的基本结构
        for (const tableName of tables) {
          try {
            // 尝试查询表结构
            tempDb.prepare(`PRAGMA table_info(${tableName})`).all();
            
            // 尝试简单查询
            tempDb.prepare(`SELECT COUNT(*) FROM ${tableName}`).get();
          } catch (tableError) {
            throw new Error(`表 ${tableName} 结构异常: ${tableError instanceof Error ? tableError.message : String(tableError)}`);
          }
        }

        tempDb.close();

        return {
          isValid: true,
          tableCount,
          tables,
          sqliteVersion
        };

      } catch (dbError) {
        tempDb.close();
        throw dbError;
      }

    } catch (error) {
      return {
        isValid: false,
        tableCount: 0,
        tables: [],
        error: error instanceof Error ? error.message : String(error)
      };
    } finally {
      // 清理临时文件
      try {
        if (fs.existsSync(tempDbPath)) {
          fs.unlinkSync(tempDbPath);
        }
      } catch (cleanupError) {
      }
    }
  }

  /**
   * 生成备份文件名（基于当前时间）
   */
  private generateBackupName(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `backup-${year}-${month}-${day}-${hours}-${minutes}-${seconds}.db`;
  }

  /**
   * 从备份文件名解析时间
   */
  private parseBackupTime(fileName: string): string | null {
    try {
      // 从文件名中提取时间部分：backup-YYYY-MM-DD-HH-mm-ss.db
      const match = fileName.match(/backup-(\d{4})-(\d{2})-(\d{2})-(\d{2})-(\d{2})-(\d{2})\.db/);
      if (!match) return null;

      const [, year, month, day, hours, minutes, seconds] = match;
      const date = new Date(
        parseInt(year),
        parseInt(month) - 1, // JavaScript月份从0开始
        parseInt(day),
        parseInt(hours),
        parseInt(minutes),
        parseInt(seconds)
      );

      return date.toISOString();
    } catch (error) {
      return null;
    }
  }

  /**
   * 预览备份文件中的分类数据
   */
  private async previewBackupCategories(backupData: Buffer): Promise<Array<{id: string, name: string}>> {
    const tempDbPath = `${this.databaseManager.getDatabasePath()}.temp-preview-${Date.now()}`;
    
    try {
      // 将备份数据写入临时文件
      fs.writeFileSync(tempDbPath, backupData);

      // 尝试打开数据库
      const tempDb = new Database(tempDbPath, { readonly: true });

      try {
        // 获取分类数据
        const categoriesResult = tempDb.prepare(`
          SELECT id, name FROM categories ORDER BY created_at DESC LIMIT 10
        `).all() as Array<{id: string, name: string}>;

        tempDb.close();
        return categoriesResult;

      } catch (dbError) {
        tempDb.close();
        throw dbError;
      }

    } catch (error) {
      throw new Error(`预览分类数据失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      // 清理临时文件
      try {
        if (fs.existsSync(tempDbPath)) {
          fs.unlinkSync(tempDbPath);
        }
      } catch (cleanupError) {
      }
    }
  }

  /**
   * 验证SQLite文件格式
   */
  private validateSQLiteFile(buffer: Buffer): boolean {
    try {
      // SQLite文件的前16个字节是固定的文件头
      const sqliteHeader = 'SQLite format 3\0';
      const headerBuffer = Buffer.from(sqliteHeader, 'utf8');
      
      if (buffer.length < 16) return false;
      
      // 检查文件头是否匹配
      return buffer.subarray(0, 16).equals(headerBuffer);
    } catch (error) {
      console.error('验证SQLite文件格式失败:', error);
      return false;
    }
  }

  /**
   * 检查是否可以执行数据库同步操作
   */
  canSync(): boolean {
    return this.ossService.isConfigured();
  }

  /**
   * 获取同步状态信息
   */
  getSyncStatus(): { canSync: boolean; lastBackupTime?: string; lastRestoreTime?: string } {
    const settings = this.settingsService.getSettings();
    return {
      canSync: this.canSync(),
      lastBackupTime: settings.lastBackupTime,
      lastRestoreTime: settings.lastRestoreTime
    };
  }

  /**
   * 带重试机制的上传方法
   */
  private async uploadWithRetry(
    key: string, 
    data: Buffer, 
    contentType: string, 
    progressCallback?: (progress: number, message: string) => void,
    maxRetries = 3
  ): Promise<{ success: boolean; message: string }> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        
        if (progressCallback && attempt > 1) {
          progressCallback(60 + (attempt - 1) * 10, `重试上传 (${attempt}/${maxRetries})...`);
        }
        
        const uploadResult = await this.ossService.uploadFile(key, data, contentType);
        
        if (uploadResult.success) {
          return {
            success: true,
            message: '上传成功'
          };
        } else {
          lastError = new Error(uploadResult.message || '上传失败');
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
      }
      
      // 如果不是最后一次尝试，等待一段时间再重试
      if (attempt < maxRetries) {
        const waitTime = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 指数退避，最多5秒
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    
    return {
      success: false,
      message: `上传失败 (已重试 ${maxRetries} 次): ${lastError?.message || '未知错误'}`
    };
  }

  /**
   * 删除OSS上的数据库备份
   */
  async deleteDatabaseBackup(backupName: string): Promise<{ success: boolean; message: string }> {
    try {
      // 检查OSS是否已配置
      if (!this.ossService.isConfigured()) {
        return {
          success: false,
          message: '未配置OSS存储，无法执行删除操作'
        };
      }


      const ossKey = `databases/${backupName}`;

      // 检查备份文件是否存在
      const existsResult = await this.ossService.fileExists(ossKey);
      if (!existsResult.success || !existsResult.exists) {
        return {
          success: false,
          message: '指定的备份文件不存在'
        };
      }

      // 删除备份文件
      const deleteResult = await this.ossService.deleteFile(ossKey);
      
      if (deleteResult.success) {
        return {
          success: true,
          message: '备份删除成功'
        };
      } else {
        console.error('❌ 数据库备份删除失败:', deleteResult.message);
        return {
          success: false,
          message: `删除失败: ${deleteResult.message}`
        };
      }

    } catch (error) {
      console.error('❌ 删除数据库备份失败:', error);
      return {
        success: false,
        message: `删除失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 重命名OSS上的数据库备份
   */
  async renameDatabaseBackup(oldBackupName: string, newBackupName: string): Promise<{ success: boolean; message: string }> {
    try {
      // 检查OSS是否已配置
      if (!this.ossService.isConfigured()) {
        return {
          success: false,
          message: '未配置OSS存储，无法执行重命名操作'
        };
      }


      // 验证新文件名格式
      if (!newBackupName.endsWith('.db')) {
        newBackupName += '.db';
      }

      // 确保新文件名符合备份命名规范
      if (!newBackupName.startsWith('backup-')) {
        return {
          success: false,
          message: '新文件名必须以 "backup-" 开头'
        };
      }

      const oldOssKey = `databases/${oldBackupName}`;
      const newOssKey = `databases/${newBackupName}`;

      // 检查原文件是否存在
      const existsResult = await this.ossService.fileExists(oldOssKey);
      if (!existsResult.success || !existsResult.exists) {
        return {
          success: false,
          message: '原备份文件不存在'
        };
      }

      // 检查新文件名是否已存在
      const newExistsResult = await this.ossService.fileExists(newOssKey);
      if (newExistsResult.success && newExistsResult.exists) {
        return {
          success: false,
          message: '新文件名已存在，请选择其他名称'
        };
      }

      // 下载原文件
      const downloadResult = await this.ossService.downloadFile(oldOssKey);
      if (!downloadResult.success || !downloadResult.data) {
        return {
          success: false,
          message: `下载原文件失败: ${downloadResult.message}`
        };
      }

      // 上传到新位置
      const uploadResult = await this.ossService.uploadFile(newOssKey, downloadResult.data, 'application/x-sqlite3');
      if (!uploadResult.success) {
        return {
          success: false,
          message: `上传新文件失败: ${uploadResult.message}`
        };
      }

      // 删除原文件
      const deleteResult = await this.ossService.deleteFile(oldOssKey);
      if (!deleteResult.success) {
        // 即使删除原文件失败，我们仍然认为重命名成功，因为新文件已经创建
      }

      return {
        success: true,
        message: '备份重命名成功'
      };

    } catch (error) {
      console.error('❌ 重命名数据库备份失败:', error);
      return {
        success: false,
        message: `重命名失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 带重试机制的下载方法
   */
  private async downloadWithRetry(
    key: string, 
    progressCallback?: (progress: number, message: string) => void,
    maxRetries = 3
  ): Promise<{ success: boolean; data?: Buffer; message: string }> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        
        if (progressCallback && attempt > 1) {
          progressCallback(20 + (attempt - 1) * 10, `重试下载 (${attempt}/${maxRetries})...`);
        }
        
        const downloadResult = await this.ossService.downloadFile(key);
        
        if (downloadResult.success && downloadResult.data) {
          return {
            success: true,
            data: downloadResult.data,
            message: '下载成功'
          };
        } else {
          lastError = new Error(downloadResult.message || '下载失败');
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
      }
      
      // 如果不是最后一次尝试，等待一段时间再重试
      if (attempt < maxRetries) {
        const waitTime = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 指数退避，最多5秒
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    
    return {
      success: false,
      message: `下载失败 (已重试 ${maxRetries} 次): ${lastError?.message || '未知错误'}`
    };
  }
}

// 导出类型定义
export type { DatabaseBackupInfo, DatabaseSyncResult, BackupListResult, RestoreResult };