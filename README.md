# Pokedex - 现代化图片管理应用

一个基于React和Electron的现代化图片管理和识别应用，支持图片分类、标签管理、物种识别和数据分析。

![应用截图](https://via.placeholder.com/800x450/4F46E5/FFFFFF?text=Pokedex)

## ✨ 主要功能

### 🖼️ 图片管理
- **双重存储**：支持本地存储和OSS对象存储，可灵活切换
- **本地存储**：所有图片和数据存储在本地，无需联网
- **OSS存储**：集成阿里云OSS等S3兼容的对象存储服务
- **分类管理**：创建自定义分类，支持缩略图设置
- **标签系统**：灵活的标签管理，支持多标签关联
- **图片查看器**：使用react-photo-view提供专业的图片浏览体验

### 🏷️ 标签与搜索
- **智能标签**：自动创建标签，支持批量编辑
- **快速搜索**：按标签、分类、名称快速定位图片
- **数据统计**：图片数量、分类统计、标签分布

### 🔍 物种识别 (需要网络)
- **AI识别**：集成物种识别API，自动识别鸟类
- **详细信息**：物种中文名、学名、栖息地、特征描述
- **识别建议**：基于图片特征提供识别建议

### 📊 数据分析
- **可视化图表**：使用ECharts展示数据统计
- **地理分布**：中国地图显示鸟类分布情况
- **趋势分析**：观察记录和物种发现趋势

### 💾 数据库特性
- **SQLite数据库**：使用better-sqlite3提供高性能本地数据存储
- **自动迁移**：从JSON数据库自动迁移到SQLite
- **事务支持**：确保数据一致性和完整性
- **性能优化**：WAL模式、索引优化、缓存配置

## 🚀 快速开始

### 前置要求
- **Node.js** 18+ 
- **npm** 或 **yarn**
- **Python 3** 和 **node-gyp** (用于编译better-sqlite3)

### 安装依赖

```bash
# 1. 克隆项目
git clone <repository-url>
cd pokedex_front

# 2. 安装依赖
npm install

# 3. 重建原生模块（重要！）
# 方法1：使用新的快捷脚本
npm run rebuild:all          # 同时为测试和 Electron 环境重新编译

# 方法2：手动分别编译
npm run rebuild:test         # 为测试环境重新编译（Node.js）
npm run rebuild:electron     # 为 Electron 运行环境重新编译

# 如果遇到 NODE_MODULE_VERSION 不匹配错误，执行：
rm -rf node_modules/better-sqlite3
npm install better-sqlite3 --save
npm run rebuild:all
```

# 测试运行应用
rm -rf node_modules/better-sqlite3
npm install better-sqlite3 --save
npm run rebuild:electron
npm run electron:dev

# 或者
rm -rf node_modules/better-sqlite3
npm install better-sqlite3 --save
npm run rebuild:electron
npm run electron:build
npm run electron:dist:linux

# 单元测试
# 测试运行应用
rm -rf node_modules/better-sqlite3
npm install better-sqlite3 --save
npm run rebuild:test
npm test

### 开发环境运行

```bash
# Web版本开发
npm run dev

# Electron版本开发
npm run electron:dev
```

### 生产构建

```bash
# Web版本构建
npm run build

# Electron版本构建
npm run electron:build

# 跨平台打包
npm run electron:dist:linux    # Linux AppImage
npm run electron:dist:win      # Windows安装包
npm run electron:dist:mac      # macOS DMG
```

## 🏗️ 技术架构

### 前端技术栈
- **React 19** + **TypeScript** - 现代化前端框架
- **Tailwind CSS** - 实用优先的CSS框架
- **Framer Motion** - 流畅的动画效果
- **React Router** - 客户端路由管理
- **Zod** - 运行时类型验证

### 桌面应用
- **Electron 28.3.3** - 跨平台桌面应用框架（兼容better-sqlite3）
- **Electron-Vite** - 快速的构建工具
- **Sharp** - 高性能图片处理
- **better-sqlite3** - 高性能SQLite数据库
- **AWS SDK v3** - 支持S3兼容的对象存储服务

### 数据库架构
- **SQLite数据库**：本地数据持久化存储
- **表结构**：
  - `categories` - 分类信息表
  - `images` - 图片信息表
  - `tags` - 标签表
  - `image_tags` - 图片标签关联表

### 构建工具
- **Vite** - 快速的前端构建工具
- **Electron Builder** - 应用打包和分发
- **ESLint + Prettier** - 代码质量和格式化

### 测试框架
- **Vitest** - 现代化单元测试框架，原生TypeScript支持
- **@vitest/coverage-v8** - V8引擎代码覆盖率分析
- **内存数据库** - 快速、隔离的SQLite测试环境
- **Mock系统** - 完整的Electron API和依赖Mock

## 📁 项目结构

```
pokedex_front/
├── src/                      # 前端源码
│   ├── components/           # React组件
│   ├── contexts/             # React Context
│   ├── hooks/                # 自定义Hooks
│   ├── services/             # API服务层
│   ├── schemas/              # Zod数据模式
│   └── types.ts              # TypeScript类型定义
├── electron/                 # Electron主进程
│   ├── main.ts              # 主进程入口
│   ├── preload.ts           # 预加载脚本
│   ├── database/            # SQLite数据库管理
│   │   └── index.ts         # 数据库主类
│   └── services/            # 后端服务
├── __tests__/               # 测试文件
│   └── electron/            # Electron单元测试
│       ├── helpers/         # 测试工具和Mock
│       ├── database/        # 数据库层测试
│       ├── services/        # 服务层测试
│       ├── main/            # 主进程测试
│       ├── preload/         # 预加载脚本测试
│       ├── integration/     # 集成测试
│       ├── edge-cases/      # 边界条件测试
│       └── performance/     # 性能基准测试
├── public/                   # 静态资源
├── release/                  # 打包输出目录
└── coverage/                 # 测试覆盖率报告
```

## 🎯 使用指南

### 本地模式 (Electron)
- **自动启动**：无需登录，直接使用
- **本地存储**：数据保存在用户目录
  - Windows: `C:\Users\<USER>\AppData\Roaming\pokedex\`
  - Linux: `/home/<USER>/.config/pokedex/`
  - macOS: `/Users/<USER>/Library/Application Support/pokedex/`
- **数据库文件**：`database.db` (SQLite数据库)
- **图片存储**：
  - `images/` - 原始图片
  - `thumbnails/` - 缩略图

### OSS对象存储配置
- **支持的服务**：阿里云OSS、AWS S3等S3兼容服务
- **配置方式**：通过应用菜单 → 文件 → 配置OSS存储
- **必填配置**：
  - **服务端点**：OSS/S3服务的访问端点
  - **区域信息**：服务所在的地理区域
  - **访问密钥ID**：用于身份验证的访问密钥
  - **访问密钥Secret**：用于身份验证的密钥
  - **存储桶名称**：OSS/S3存储桶的名称
  - **路径前缀**：可选，用于文件路径的前缀
- **切换存储**：应用菜单 → 文件 → 切换存储类型
- **连接测试**：配置完成后可进行连接测试验证
- **数据安全**：OSS配置安全存储在本地，不会上传到服务器

### Web模式 (浏览器)
- **在线功能**：需要网络连接使用完整功能
- **邮箱验证**：使用验证码登录系统
- **远程API**：连接到后端服务器

### 快捷键 (Electron版本)
- `Ctrl/Cmd + N` - 新建分类
- `Ctrl/Cmd + O` - 导入图片
- `Ctrl/Cmd + F` - 搜索
- `Ctrl/Cmd + ,` - 设置
- `F11` - 全屏切换
- `Ctrl/Cmd + ?` - 显示快捷键帮助

## 🔧 开发指南

### 代码质量
```bash
npm run type-check    # TypeScript类型检查
npm run lint          # ESLint代码检查
npm run lint:fix      # 自动修复代码问题
npm run format        # Prettier代码格式化
npm run check-all     # 运行所有检查
```

### 单元测试

本项目建立了完整的Electron单元测试体系，覆盖所有核心功能。

#### 测试框架和工具
- **Vitest** - 现代化测试框架，原生TypeScript和ESM支持
- **@vitest/coverage-v8** - V8引擎覆盖率分析
- **内存数据库** - 快速、隔离的测试环境
- **Mock系统** - 完整的Electron API和外部依赖Mock

#### 运行测试
```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm test -- --coverage

# 运行特定测试文件
npm test -- __tests__/electron/services/CategoryService.test.ts

# 运行特定测试模式
npm test -- __tests__/electron/edge-cases/     # 边界条件测试
npm test -- __tests__/electron/performance/    # 性能基准测试
npm test -- __tests__/electron/integration/    # 集成测试

# 监控模式（开发时使用）
npm test -- --watch
```

#### 测试覆盖情况
- **📊 测试统计**: 16个测试文件，280个测试用例，98.5%通过率
- **🎯 覆盖模块**:
  - ✅ 数据库层 (DatabaseManager)
  - ✅ 服务层 (CategoryService, ImageService, TagService, SettingsService, OSSService)
  - ✅ 主进程 (IPC处理器, 窗口管理, 菜单系统)
  - ✅ 预加载脚本 (API暴露和类型安全)
  - ✅ 集成测试 (跨服务数据一致性, 文件系统操作, OSS集成)
  - ✅ 边界测试 (错误处理, 异常恢复, 安全验证)
  - ✅ 性能测试 (负载测试, 内存监控, 启动性能)
  - ✅ OSS功能测试 (对象存储操作, 配置验证, 存储类型切换)

#### 测试架构
```
__tests__/
├── electron/
│   ├── helpers/                  # 测试工具和Mock
│   │   ├── test-database.ts      # 测试数据库管理
│   │   ├── electron-mocks.ts     # Electron API Mock
│   │   └── test-data-generator.ts # 测试数据生成器
│   ├── database/                 # 数据库层测试
│   │   └── DatabaseManager.test.ts
│   ├── services/                 # 服务层测试
│   │   ├── CategoryService.test.ts
│   │   ├── ImageService.test.ts
│   │   ├── TagService.test.ts
│   │   ├── SettingsService.test.ts
│   │   ├── OSSService.test.ts
│   │   └── oss-config-validation.test.ts
│   ├── main/                     # 主进程测试
│   │   ├── main-components.test.ts
│   │   └── ipc-handlers.test.ts
│   ├── preload/                  # 预加载脚本测试
│   │   └── preload-standalone.test.ts
│   ├── integration/              # 集成测试
│   │   ├── database-integration.test.ts
│   │   ├── filesystem-integration.test.ts
│   │   └── oss-integration.test.ts
│   ├── edge-cases/               # 边界条件测试
│   │   └── error-handling.test.ts
│   └── performance/              # 性能基准测试
│       └── benchmarks.test.ts
```

#### 性能基准
测试建立了完整的性能基准：
- **数据库操作**: ≥30 分类创建/秒
- **图片处理**: ≥2 上传/秒, ≥10 删除/秒
- **内存管理**: 峰值增长 <200MB, 清理后 <100MB
- **启动性能**: <1秒服务初始化
- **搜索性能**: 复杂标签搜索 <5秒

#### 调试和诊断
```bash
npm run electron:start    # 启动构建后的Electron应用
node --version            # 检查Node.js版本
npm list uuid sharp better-sqlite3      # 检查关键依赖

# 调试特定测试
npm test -- __tests__/path/to/test.ts --reporter=verbose

# 生成详细的覆盖率报告
npm test -- --coverage --reporter=verbose
```

### API模式切换
应用自动检测运行环境：
- **Electron环境**：使用本地SQLite数据库
- **浏览器环境**：使用远程API服务

### 数据库操作
```bash
# 查看数据库状态
sqlite3 ~/.config/pokedex/database.db ".tables"

# 导出数据
sqlite3 ~/.config/pokedex/database.db ".dump" > backup.sql

# 清空数据库（谨慎使用）
rm ~/.config/pokedex/database.db
```

## 🚀 部署说明

### 支持平台
- ✅ **Linux** - AppImage格式 (已测试)
- ✅ **Windows** - NSIS安装包 + 便携版
- ✅ **macOS** - DMG安装包

### 打包输出
```
release/
├── linux-unpacked/                          # Linux解包版本
├── Pokedex-1.0.0.AppImage     # Linux安装包
├── win-unpacked/                            # Windows解包版本  
├── Pokedex-Setup-1.0.0.exe   # Windows安装包
└── Pokedex-1.0.0-x64.exe     # Windows便携版
```

### 编译注意事项
1. **better-sqlite3编译**：
   - 需要Python 3和node-gyp
   - 使用Electron 28.3.3确保兼容性
   - 打包时会自动重新编译原生模块

2. **跨平台编译**：
   - Linux: 需要AppImage相关工具
   - Windows: 需要NSIS或Wine（在Linux/Mac上编译）
   - macOS: 需要在macOS系统上编译

## 🐛 故障排除

### 常见问题

**better-sqlite3编译错误**
- 问题：`NODE_MODULE_VERSION mismatch`（Node.js 版本不匹配）
- 解决：
  ```bash
  # 方法1：使用快捷脚本（推荐）
  npm run rebuild:all                           # 同时为测试和 Electron 环境重新编译
  
  # 方法2：手动分别编译
  npm run rebuild:test                          # 为测试环境
  npm run rebuild:electron                      # 为 Electron 环境
  
  # 方法3：完全重新安装（如果以上方法失败）
  rm -rf node_modules/better-sqlite3
  npm install better-sqlite3 --save
  npm run rebuild:electron
  ```

**数据库迁移失败**
- 检查JSON文件格式是否正确
**数据库初始化失败**
- 检查磁盘空间是否充足
- 确认数据库文件夹有写入权限
- 查看控制台错误信息

**图片无法显示**
- 检查图片路径和权限
- 确认Sharp模块正确安装
- 查看控制台错误信息

**数据库锁定错误**
- 确保没有多个应用实例同时运行
- 检查数据库文件权限
- 删除WAL和SHM文件后重试

**OSS配置和连接问题**
- 检查OSS服务端点和区域配置是否正确
- 确认访问密钥ID和密钥Secret有效
- 验证存储桶名称和权限设置
- 测试网络连通性到OSS服务端点
- 检查防火墙或代理设置

**OSS文件上传失败**
- 确认存储桶有写入权限
- 检查文件大小是否超过限制
- 验证路径前缀配置
- 查看详细错误信息进行诊断

**存储类型切换异常**
- 确保OSS配置完整且有效
- 检查本地存储权限
- 验证数据库连接状态
- 重启应用后重试

### 获取帮助
- 查看 [完整迁移文档](./COMPLETE_MIGRATION_PLAN.md)
- 查看 [Electron单元测试覆盖计划](./plan/9.Electron单元测试覆盖计划.md)
- 检查 [构建日志](./release/builder-debug.yml)
- 运行 `npm test -- --coverage` 查看测试覆盖率报告
- 提交Issue到项目仓库

## 📋 开发路线图

- [x] 基础图片管理功能
- [x] 本地Electron版本
- [x] 标签系统和搜索
- [x] 跨平台打包
- [x] SQLite数据库支持
- [x] **完整单元测试体系** (16个测试文件，280个测试用例，98.5%通过率)
- [x] **性能基准测试** (数据库、图片处理、内存管理、启动时间)
- [x] **集成测试覆盖** (跨服务数据一致性、文件系统操作、OSS集成)
- [x] **边界条件测试** (错误处理、异常恢复、安全验证)
- [x] **OSS对象存储支持** (阿里云OSS、AWS S3等S3兼容服务)
- [x] **存储类型切换** (本地存储与OSS存储无缝切换)
- [x] **OSS配置管理** (原生对话框配置、连接测试、安全存储)
- [ ] 数据导入导出增强
- [ ] 图片批量处理
- [ ] 插件系统
- [ ] 云端同步功能

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Issues和Pull Requests！

---

**Pokedex** - 让图片管理变得简单而强大 🚀